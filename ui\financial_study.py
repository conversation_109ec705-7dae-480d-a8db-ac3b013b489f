#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الدراسة المالية
Financial Study Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox

class FinancialStudyFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()

    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="📘 الدراسة المالية")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="الدراسة المالية للمشروع",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))

        # إنشاء الأقسام المختلفة
        self.create_working_capital_section()
        self.create_pre_operation_section()
        self.create_capital_summary_section()
        self.create_products_revenue_section()
        self.create_annual_summary_section()

        # أزرار التحكم
        self.create_control_buttons()

        # متغيرات الحفظ
        self.fields = {}

    def create_working_capital_section(self):
        """إنشاء قسم رأس المال العامل"""
        working_capital_frame = ctk.CTkFrame(self.scrollable_frame)
        working_capital_frame.pack(fill="x", padx=20, pady=10)

        wc_title = ctk.CTkLabel(
            working_capital_frame,
            text="💰 أولًا: رأس المال العامل – تكاليف المشروع التشغيلية (شهريًا)",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        wc_title.pack(pady=(15, 10))

        # 1. التكاليف الثابتة
        fixed_costs_frame = ctk.CTkFrame(working_capital_frame)
        fixed_costs_frame.pack(fill="x", padx=20, pady=10)

        fixed_title = ctk.CTkLabel(
            fixed_costs_frame,
            text="1. التكاليف الثابتة",
            font=self.arabic_font_bold
        )
        fixed_title.pack(pady=10)

        # جدول التكاليف الثابتة
        self.fields['fixed_costs'] = {}
        fixed_cost_items = [
            ("fixed_salaries", "الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)"),
            ("rent", "الإيجار"),
            ("maintenance", "الصيانة"),
            ("marketing", "التسويق والدعاية"),
            ("other_fixed", "تكاليف ثابتة أخرى")
        ]

        for key, label in fixed_cost_items:
            self.create_cost_field(fixed_costs_frame, key, label, "fixed_costs")

        # مجموع التكاليف الثابتة
        self.fixed_total_frame = ctk.CTkFrame(fixed_costs_frame)
        self.fixed_total_frame.pack(fill="x", padx=20, pady=10)

        self.fixed_total_label = ctk.CTkLabel(
            self.fixed_total_frame,
            text="🔹 مجموع التكاليف الثابتة: 0 دينار",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        self.fixed_total_label.pack(pady=10)

        # 2. التكاليف المتغيرة
        variable_costs_frame = ctk.CTkFrame(working_capital_frame)
        variable_costs_frame.pack(fill="x", padx=20, pady=10)

        variable_title = ctk.CTkLabel(
            variable_costs_frame,
            text="2. التكاليف المتغيرة",
            font=self.arabic_font_bold
        )
        variable_title.pack(pady=10)

        # جدول التكاليف المتغيرة
        self.fields['variable_costs'] = {}
        variable_cost_items = [
            ("raw_materials", "المواد الخام"),
            ("direct_labor", "أجور العمال المباشرين"),
            ("utilities", "فواتير الماء والكهرباء والهاتف والإنترنت"),
            ("transportation", "أجور النقل والمواصلات"),
            ("other_variable", "تكاليف متغيرة أخرى")
        ]

        for key, label in variable_cost_items:
            self.create_cost_field(variable_costs_frame, key, label, "variable_costs")

        # مجاميع التكاليف المتغيرة
        self.variable_totals_frame = ctk.CTkFrame(variable_costs_frame)
        self.variable_totals_frame.pack(fill="x", padx=20, pady=10)

        self.variable_without_materials_label = ctk.CTkLabel(
            self.variable_totals_frame,
            text="🔸 مجموع التكاليف المتغيرة (بدون المواد الخام): 0 دينار",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        self.variable_without_materials_label.pack(pady=5)

        self.variable_with_materials_label = ctk.CTkLabel(
            self.variable_totals_frame,
            text="🔹 مجموع التكاليف المتغيرة (مع المواد الخام): 0 دينار",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        self.variable_with_materials_label.pack(pady=5)

        # إجمالي رأس المال العامل
        self.working_capital_total_frame = ctk.CTkFrame(working_capital_frame)
        self.working_capital_total_frame.pack(fill="x", padx=20, pady=15)

        wc_total_title = ctk.CTkLabel(
            self.working_capital_total_frame,
            text="✅ إجمالي رأس المال العامل (تكاليف المشروع التشغيلية)",
            font=self.arabic_font_bold
        )
        wc_total_title.pack(pady=5)

        wc_formula = ctk.CTkLabel(
            self.working_capital_total_frame,
            text="= مجموع التكاليف الثابتة + مجموع التكاليف المتغيرة (مع المواد الخام)",
            font=self.arabic_font,
            text_color=("#6b7280", "#9ca3af")
        )
        wc_formula.pack(pady=2)

        self.working_capital_total_label = ctk.CTkLabel(
            self.working_capital_total_frame,
            text="💵 المبلغ الإجمالي بالدينار: 0",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        self.working_capital_total_label.pack(pady=10)

    def create_cost_field(self, parent, key, label, section):
        """إنشاء حقل تكلفة"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", padx=20, pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=self.arabic_font,
            width=400,
            anchor="e"
        )
        label_widget.pack(side="right", padx=(0, 20))

        # حقل الإدخال
        entry = ctk.CTkEntry(
            field_frame,
            font=self.arabic_font,
            width=150,
            justify="center",
            placeholder_text="0"
        )
        entry.pack(side="right", padx=20)
        entry.bind('<KeyRelease>', lambda e: self.calculate_totals())

        # وحدة العملة
        currency_label = ctk.CTkLabel(
            field_frame,
            text="دينار",
            font=self.arabic_font,
            width=50
        )
        currency_label.pack(side="right", padx=5)

        self.fields[section][key] = entry

    def create_pre_operation_section(self):
        """إنشاء قسم النفقات التأسيسية"""
        pre_op_frame = ctk.CTkFrame(self.scrollable_frame)
        pre_op_frame.pack(fill="x", padx=20, pady=10)

        pre_op_title = ctk.CTkLabel(
            pre_op_frame,
            text="🏗️ ثانيًا: النفقات التأسيسية – ما قبل التشغيل",
            font=self.arabic_font_bold,
            text_color=("#8b5cf6", "#8b5cf6")
        )
        pre_op_title.pack(pady=(15, 10))

        # جدول النفقات التأسيسية
        self.fields['pre_operation'] = {}
        pre_op_items = [
            ("registration_licensing", "التسجيل والترخيص"),
            ("utilities_connection", "توصيل الخدمات"),
            ("location_setup", "خلو / تجهيز الموقع"),
            ("initial_setup", "تجهيز أولي"),
            ("other_pre_operation", "أخرى")
        ]

        for key, label in pre_op_items:
            self.create_cost_field(pre_op_frame, key, label, "pre_operation")

        # مجموع النفقات التأسيسية
        self.pre_op_total_frame = ctk.CTkFrame(pre_op_frame)
        self.pre_op_total_frame.pack(fill="x", padx=20, pady=10)

        self.pre_op_total_label = ctk.CTkLabel(
            self.pre_op_total_frame,
            text="مجموع نفقات ما قبل التشغيل: 0 دينار",
            font=self.arabic_font_bold,
            text_color=("#8b5cf6", "#8b5cf6")
        )
        self.pre_op_total_label.pack(pady=10)

    def create_capital_summary_section(self):
        """إنشاء قسم ملخص رأس المال"""
        capital_frame = ctk.CTkFrame(self.scrollable_frame)
        capital_frame.pack(fill="x", padx=20, pady=10)

        capital_title = ctk.CTkLabel(
            capital_frame,
            text="💼 ثالثًا: إجمالي رأس مال المشروع المتوقع",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        capital_title.pack(pady=(15, 10))

        # جدول رأس المال
        capital_table_frame = ctk.CTkFrame(capital_frame)
        capital_table_frame.pack(fill="x", padx=20, pady=10)

        # رأس المال الثابت
        fixed_capital_frame = ctk.CTkFrame(capital_table_frame, fg_color="transparent")
        fixed_capital_frame.pack(fill="x", padx=20, pady=5)

        fixed_capital_label = ctk.CTkLabel(
            fixed_capital_frame,
            text="رأس المال الثابت (تكاليف المشروع الرأسمالية – المعدات، الأجهزة...)",
            font=self.arabic_font,
            width=500,
            anchor="e"
        )
        fixed_capital_label.pack(side="right", padx=(0, 20))

        self.fixed_capital_entry = ctk.CTkEntry(
            fixed_capital_frame,
            font=self.arabic_font,
            width=150,
            justify="center",
            placeholder_text="0"
        )
        self.fixed_capital_entry.pack(side="right", padx=20)
        self.fixed_capital_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())

        currency_label1 = ctk.CTkLabel(
            fixed_capital_frame,
            text="دينار",
            font=self.arabic_font,
            width=50
        )
        currency_label1.pack(side="right", padx=5)

        # رأس المال العامل
        working_capital_display_frame = ctk.CTkFrame(capital_table_frame, fg_color="transparent")
        working_capital_display_frame.pack(fill="x", padx=20, pady=5)

        working_capital_display_label = ctk.CTkLabel(
            working_capital_display_frame,
            text="رأس المال العامل (لأول شهر فقط)",
            font=self.arabic_font,
            width=500,
            anchor="e"
        )
        working_capital_display_label.pack(side="right", padx=(0, 20))

        self.working_capital_display_label = ctk.CTkLabel(
            working_capital_display_frame,
            text="0 دينار",
            font=self.arabic_font,
            width=150,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.working_capital_display_label.pack(side="right", padx=20)

        # إجمالي رأس المال
        total_capital_frame = ctk.CTkFrame(capital_frame)
        total_capital_frame.pack(fill="x", padx=20, pady=15)

        total_capital_title = ctk.CTkLabel(
            total_capital_frame,
            text="🔷 إجمالي رأس مال المشروع المتوقع (مجموع النفقات والتكاليف)",
            font=self.arabic_font_bold
        )
        total_capital_title.pack(pady=5)

        self.total_capital_label = ctk.CTkLabel(
            total_capital_frame,
            text="0 دينار عراقي",
            font=self.arabic_font_large,
            text_color=("#059669", "#059669")
        )
        self.total_capital_label.pack(pady=10)

    def create_products_revenue_section(self):
        """إنشاء قسم المنتجات والإيرادات"""
        products_frame = ctk.CTkFrame(self.scrollable_frame)
        products_frame.pack(fill="x", padx=20, pady=10)

        products_title = ctk.CTkLabel(
            products_frame,
            text="📊 حساب الربح والخسارة الشهري لكل منتج",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        products_title.pack(pady=(15, 10))

        # جدول المنتجات
        products_table_frame = ctk.CTkFrame(products_frame)
        products_table_frame.pack(fill="x", padx=20, pady=10)

        # عناوين الجدول
        headers_frame = ctk.CTkFrame(products_table_frame, fg_color="transparent")
        headers_frame.pack(fill="x", padx=10, pady=5)

        headers = [
            ("الربح", 100),
            ("الإيراد", 100),
            ("سعر البيع", 100),
            ("إجمالي التكلفة", 120),
            ("تكلفة الوحدة", 100),
            ("عدد الوحدات", 100),
            ("المنتج/الخدمة", 150)
        ]

        for header_text, width in headers:
            header = ctk.CTkLabel(
                headers_frame,
                text=header_text,
                font=self.arabic_font_bold,
                width=width,
                fg_color=("#3b82f6", "#3b82f6"),
                corner_radius=5
            )
            if header_text == "المنتج/الخدمة":
                header.pack(side="right", padx=2)
            else:
                header.pack(side="left", padx=2)

        # إطار الصفوف
        self.products_rows_frame = ctk.CTkFrame(products_table_frame)
        self.products_rows_frame.pack(fill="x", padx=10, pady=5)

        # قائمة الصفوف
        self.products_rows = []

        # إضافة صفوف افتراضية
        for i in range(4):
            self.add_product_row()

        # أزرار إدارة الجدول
        products_buttons_frame = ctk.CTkFrame(products_frame, fg_color="transparent")
        products_buttons_frame.pack(fill="x", padx=20, pady=10)

        add_product_btn = ctk.CTkButton(
            products_buttons_frame,
            text="➕ إضافة منتج",
            font=self.arabic_font,
            width=120,
            height=30,
            command=self.add_product_row
        )
        add_product_btn.pack(side="right", padx=5)

        remove_product_btn = ctk.CTkButton(
            products_buttons_frame,
            text="➖ حذف منتج",
            font=self.arabic_font,
            width=120,
            height=30,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.remove_product_row
        )
        remove_product_btn.pack(side="right", padx=5)

    def add_product_row(self):
        """إضافة صف منتج جديد"""
        row_frame = ctk.CTkFrame(self.products_rows_frame, fg_color="transparent")
        row_frame.pack(fill="x", pady=2)

        # الربح (محسوب تلقائياً)
        profit_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=100,
            justify="center",
            state="readonly"
        )
        profit_entry.pack(side="left", padx=2)

        # الإيراد (محسوب تلقائياً)
        revenue_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=100,
            justify="center",
            state="readonly"
        )
        revenue_entry.pack(side="left", padx=2)

        # سعر البيع
        selling_price_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=100,
            justify="center"
        )
        selling_price_entry.pack(side="left", padx=2)
        selling_price_entry.bind('<KeyRelease>', lambda e: self.calculate_product_totals())

        # إجمالي التكلفة (محسوب تلقائياً)
        total_cost_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center",
            state="readonly"
        )
        total_cost_entry.pack(side="left", padx=2)

        # تكلفة الوحدة
        unit_cost_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=100,
            justify="center"
        )
        unit_cost_entry.pack(side="left", padx=2)
        unit_cost_entry.bind('<KeyRelease>', lambda e: self.calculate_product_totals())

        # عدد الوحدات
        quantity_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=100,
            justify="center"
        )
        quantity_entry.pack(side="left", padx=2)
        quantity_entry.bind('<KeyRelease>', lambda e: self.calculate_product_totals())

        # اسم المنتج
        product_name_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=150,
            justify="right"
        )
        product_name_entry.pack(side="right", padx=2)

        # حفظ مراجع الحقول
        row_data = {
            'frame': row_frame,
            'product_name': product_name_entry,
            'quantity': quantity_entry,
            'unit_cost': unit_cost_entry,
            'total_cost': total_cost_entry,
            'selling_price': selling_price_entry,
            'revenue': revenue_entry,
            'profit': profit_entry
        }

        self.products_rows.append(row_data)

    def remove_product_row(self):
        """حذف آخر صف منتج"""
        if len(self.products_rows) > 1:
            row_data = self.products_rows.pop()
            row_data['frame'].destroy()
            self.calculate_product_totals()

    def create_annual_summary_section(self):
        """إنشاء قسم الملخص السنوي"""
        annual_frame = ctk.CTkFrame(self.scrollable_frame)
        annual_frame.pack(fill="x", padx=20, pady=10)

        annual_title = ctk.CTkLabel(
            annual_frame,
            text="📈 ملخص الأرباح والخسائر السنوي",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        annual_title.pack(pady=(15, 10))

        # جدول الملخص السنوي
        summary_table_frame = ctk.CTkFrame(annual_frame)
        summary_table_frame.pack(fill="x", padx=20, pady=10)

        # الإيرادات السنوية
        annual_revenue_frame = ctk.CTkFrame(summary_table_frame, fg_color="transparent")
        annual_revenue_frame.pack(fill="x", padx=20, pady=5)

        annual_revenue_label = ctk.CTkLabel(
            annual_revenue_frame,
            text="إجمالي الإيرادات السنوية",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        annual_revenue_label.pack(side="right", padx=(0, 20))

        self.annual_revenue_label = ctk.CTkLabel(
            annual_revenue_frame,
            text="0 دينار",
            font=self.arabic_font_bold,
            width=150,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.annual_revenue_label.pack(side="right", padx=20)

        # التكاليف التشغيلية السنوية
        annual_costs_frame = ctk.CTkFrame(summary_table_frame, fg_color="transparent")
        annual_costs_frame.pack(fill="x", padx=20, pady=5)

        annual_costs_label = ctk.CTkLabel(
            annual_costs_frame,
            text="إجمالي التكاليف التشغيلية السنوية (ثابتة + متغيرة)",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        annual_costs_label.pack(side="right", padx=(0, 20))

        self.annual_costs_label = ctk.CTkLabel(
            annual_costs_frame,
            text="0 دينار",
            font=self.arabic_font_bold,
            width=150,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.annual_costs_label.pack(side="right", padx=20)

        # الإهلاك السنوي
        depreciation_frame = ctk.CTkFrame(summary_table_frame, fg_color="transparent")
        depreciation_frame.pack(fill="x", padx=20, pady=5)

        depreciation_label = ctk.CTkLabel(
            depreciation_frame,
            text="قيمة الإهلاك السنوي (لرأس المال الثابت)",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        depreciation_label.pack(side="right", padx=(0, 20))

        self.depreciation_entry = ctk.CTkEntry(
            depreciation_frame,
            font=self.arabic_font,
            width=150,
            justify="center",
            placeholder_text="0"
        )
        self.depreciation_entry.pack(side="right", padx=20)
        self.depreciation_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())

        # صافي الأرباح السنوية
        net_profit_frame = ctk.CTkFrame(annual_frame)
        net_profit_frame.pack(fill="x", padx=20, pady=15)

        net_profit_title = ctk.CTkLabel(
            net_profit_frame,
            text="✅ صافي الأرباح السنوية = الإيرادات - التكاليف - الإهلاك",
            font=self.arabic_font_bold
        )
        net_profit_title.pack(pady=5)

        self.net_profit_label = ctk.CTkLabel(
            net_profit_frame,
            text="0 دينار عراقي",
            font=self.arabic_font_large,
            text_color=("#059669", "#059669")
        )
        self.net_profit_label.pack(pady=10)

    def calculate_totals(self):
        """حساب جميع الإجماليات"""
        try:
            # حساب التكاليف الثابتة
            fixed_total = 0
            for key, entry in self.fields.get('fixed_costs', {}).items():
                try:
                    value = float(entry.get() or 0)
                    fixed_total += value
                except ValueError:
                    continue

            self.fixed_total_label.configure(
                text=f"🔹 مجموع التكاليف الثابتة: {fixed_total:,.0f} دينار"
            )

            # حساب التكاليف المتغيرة
            variable_total = 0
            variable_without_materials = 0
            raw_materials_cost = 0

            for key, entry in self.fields.get('variable_costs', {}).items():
                try:
                    value = float(entry.get() or 0)
                    variable_total += value
                    if key == 'raw_materials':
                        raw_materials_cost = value
                    else:
                        variable_without_materials += value
                except ValueError:
                    continue

            self.variable_without_materials_label.configure(
                text=f"🔸 مجموع التكاليف المتغيرة (بدون المواد الخام): {variable_without_materials:,.0f} دينار"
            )

            self.variable_with_materials_label.configure(
                text=f"🔹 مجموع التكاليف المتغيرة (مع المواد الخام): {variable_total:,.0f} دينار"
            )

            # حساب رأس المال العامل
            working_capital = fixed_total + variable_total
            self.working_capital_total_label.configure(
                text=f"💵 المبلغ الإجمالي بالدينار: {working_capital:,.0f}"
            )

            self.working_capital_display_label.configure(
                text=f"{working_capital:,.0f} دينار"
            )

            # حساب النفقات التأسيسية
            pre_op_total = 0
            for key, entry in self.fields.get('pre_operation', {}).items():
                try:
                    value = float(entry.get() or 0)
                    pre_op_total += value
                except ValueError:
                    continue

            self.pre_op_total_label.configure(
                text=f"مجموع نفقات ما قبل التشغيل: {pre_op_total:,.0f} دينار"
            )

            # حساب إجمالي رأس المال
            try:
                fixed_capital = float(self.fixed_capital_entry.get() or 0)
            except ValueError:
                fixed_capital = 0

            total_capital = fixed_capital + working_capital + pre_op_total
            self.total_capital_label.configure(
                text=f"{total_capital:,.0f} دينار عراقي"
            )

            # حساب الملخص السنوي
            annual_revenue = 0
            for row_data in self.products_rows:
                try:
                    revenue = float(row_data['revenue'].get() or 0)
                    annual_revenue += revenue * 12  # ضرب في 12 شهر
                except (ValueError, tk.TclError):
                    continue

            annual_costs = (fixed_total + variable_total) * 12

            try:
                depreciation = float(self.depreciation_entry.get() or 0)
            except ValueError:
                depreciation = 0

            net_profit = annual_revenue - annual_costs - depreciation

            self.annual_revenue_label.configure(
                text=f"{annual_revenue:,.0f} دينار"
            )

            self.annual_costs_label.configure(
                text=f"{annual_costs:,.0f} دينار"
            )

            self.net_profit_label.configure(
                text=f"{net_profit:,.0f} دينار عراقي"
            )

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {e}")

    def calculate_product_totals(self):
        """حساب إجماليات المنتجات"""
        for row_data in self.products_rows:
            try:
                quantity = float(row_data['quantity'].get() or 0)
                unit_cost = float(row_data['unit_cost'].get() or 0)
                selling_price = float(row_data['selling_price'].get() or 0)

                total_cost = quantity * unit_cost
                revenue = quantity * selling_price
                profit = revenue - total_cost

                # تحديث الحقول المحسوبة
                row_data['total_cost'].configure(state="normal")
                row_data['total_cost'].delete(0, tk.END)
                row_data['total_cost'].insert(0, f"{total_cost:,.0f}")
                row_data['total_cost'].configure(state="readonly")

                row_data['revenue'].configure(state="normal")
                row_data['revenue'].delete(0, tk.END)
                row_data['revenue'].insert(0, f"{revenue:,.0f}")
                row_data['revenue'].configure(state="readonly")

                row_data['profit'].configure(state="normal")
                row_data['profit'].delete(0, tk.END)
                row_data['profit'].insert(0, f"{profit:,.0f}")
                row_data['profit'].configure(state="readonly")

            except (ValueError, tk.TclError):
                continue

        # إعادة حساب الإجماليات العامة
        self.calculate_totals()

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الدراسة المالية",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)

        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)

        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: نقطة التعادل ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)

        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: مستلزمات الإنتاج",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)

    def save_data(self):
        """حفظ البيانات"""
        try:
            # جمع بيانات التكاليف
            fixed_costs = {}
            for key, entry in self.fields.get('fixed_costs', {}).items():
                try:
                    fixed_costs[key] = float(entry.get() or 0)
                except ValueError:
                    fixed_costs[key] = 0

            variable_costs = {}
            for key, entry in self.fields.get('variable_costs', {}).items():
                try:
                    variable_costs[key] = float(entry.get() or 0)
                except ValueError:
                    variable_costs[key] = 0

            pre_operation = {}
            for key, entry in self.fields.get('pre_operation', {}).items():
                try:
                    pre_operation[key] = float(entry.get() or 0)
                except ValueError:
                    pre_operation[key] = 0

            # جمع بيانات المنتجات
            products = []
            for row_data in self.products_rows:
                product_name = row_data['product_name'].get().strip()
                if product_name:
                    try:
                        products.append({
                            'name': product_name,
                            'quantity': float(row_data['quantity'].get() or 0),
                            'unit_cost': float(row_data['unit_cost'].get() or 0),
                            'selling_price': float(row_data['selling_price'].get() or 0),
                            'total_cost': float(row_data['quantity'].get() or 0) * float(row_data['unit_cost'].get() or 0),
                            'revenue': float(row_data['quantity'].get() or 0) * float(row_data['selling_price'].get() or 0),
                            'profit': (float(row_data['quantity'].get() or 0) * float(row_data['selling_price'].get() or 0)) -
                                     (float(row_data['quantity'].get() or 0) * float(row_data['unit_cost'].get() or 0))
                        })
                    except ValueError:
                        continue

            # جمع البيانات الأخرى
            try:
                fixed_capital = float(self.fixed_capital_entry.get() or 0)
            except ValueError:
                fixed_capital = 0

            try:
                depreciation = float(self.depreciation_entry.get() or 0)
            except ValueError:
                depreciation = 0

            # حساب الإجماليات
            working_capital = sum(fixed_costs.values()) + sum(variable_costs.values())
            total_capital = fixed_capital + working_capital + sum(pre_operation.values())

            annual_revenue = sum(product['revenue'] for product in products) * 12
            annual_costs = working_capital * 12
            net_profit = annual_revenue - annual_costs - depreciation

            data = {
                'fixed_costs': fixed_costs,
                'variable_costs': variable_costs,
                'pre_operation_expenses': pre_operation,
                'products': products,
                'fixed_capital': fixed_capital,
                'working_capital': working_capital,
                'total_project_capital': total_capital,
                'annual_revenues': annual_revenue,
                'annual_operating_costs': annual_costs,
                'annual_depreciation': depreciation,
                'net_annual_profit': net_profit
            }

            self.data_manager.set_data("financial_study", data)

            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ الدراسة المالية بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)

    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("financial_study")

            # تحميل التكاليف الثابتة
            fixed_costs = data.get('fixed_costs', {})
            for key, entry in self.fields.get('fixed_costs', {}).items():
                value = fixed_costs.get(key, 0)
                entry.delete(0, tk.END)
                entry.insert(0, str(value))

            # تحميل التكاليف المتغيرة
            variable_costs = data.get('variable_costs', {})
            for key, entry in self.fields.get('variable_costs', {}).items():
                value = variable_costs.get(key, 0)
                entry.delete(0, tk.END)
                entry.insert(0, str(value))

            # تحميل النفقات التأسيسية
            pre_operation = data.get('pre_operation_expenses', {})
            for key, entry in self.fields.get('pre_operation', {}).items():
                value = pre_operation.get(key, 0)
                entry.delete(0, tk.END)
                entry.insert(0, str(value))

            # تحميل رأس المال الثابت
            fixed_capital = data.get('fixed_capital', 0)
            self.fixed_capital_entry.delete(0, tk.END)
            self.fixed_capital_entry.insert(0, str(fixed_capital))

            # تحميل الإهلاك
            depreciation = data.get('annual_depreciation', 0)
            self.depreciation_entry.delete(0, tk.END)
            self.depreciation_entry.insert(0, str(depreciation))

            # تحميل المنتجات
            products = data.get('products', [])
            for i, product in enumerate(products):
                if i < len(self.products_rows):
                    row_data = self.products_rows[i]
                else:
                    self.add_product_row()
                    row_data = self.products_rows[-1]

                row_data['product_name'].delete(0, tk.END)
                row_data['product_name'].insert(0, product.get('name', ''))
                row_data['quantity'].delete(0, tk.END)
                row_data['quantity'].insert(0, str(product.get('quantity', '')))
                row_data['unit_cost'].delete(0, tk.END)
                row_data['unit_cost'].insert(0, str(product.get('unit_cost', '')))
                row_data['selling_price'].delete(0, tk.END)
                row_data['selling_price'].insert(0, str(product.get('selling_price', '')))

            # إعادة حساب الإجماليات
            self.calculate_product_totals()
            self.calculate_totals()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        # مسح التكاليف
        for section in ['fixed_costs', 'variable_costs', 'pre_operation']:
            for entry in self.fields.get(section, {}).values():
                entry.delete(0, tk.END)

        # مسح رأس المال والإهلاك
        self.fixed_capital_entry.delete(0, tk.END)
        self.depreciation_entry.delete(0, tk.END)

        # مسح المنتجات
        for row_data in self.products_rows:
            for field in ['product_name', 'quantity', 'unit_cost', 'selling_price']:
                row_data[field].delete(0, tk.END)

            for field in ['total_cost', 'revenue', 'profit']:
                row_data[field].configure(state="normal")
                row_data[field].delete(0, tk.END)
                row_data[field].configure(state="readonly")

        # إعادة تعيين الإجماليات
        self.calculate_totals()

    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("breakeven_calculator")

    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("production_requirements")