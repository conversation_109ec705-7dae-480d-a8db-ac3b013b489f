#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التحليل الرباعي SWOT
SWOT Analysis Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk

class SWOTAnalysisFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="📈 التحليل الرباعي SWOT")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="التحليل الرباعي - SWOT Analysis",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # شرح التحليل الرباعي
        explanation_frame = ctk.CTkFrame(self.scrollable_frame)
        explanation_frame.pack(fill="x", padx=20, pady=10)
        
        explanation_text = """
التحليل الرباعي (SWOT) هو أداة تحليلية تساعد في تقييم:
• نقاط القوة (Strengths): العوامل الداخلية الإيجابية
• نقاط الضعف (Weaknesses): العوامل الداخلية السلبية  
• الفرص (Opportunities): العوامل الخارجية الإيجابية
• التهديدات (Threats): العوامل الخارجية السلبية
        """
        
        explanation_label = ctk.CTkLabel(
            explanation_frame,
            text=explanation_text,
            font=self.arabic_font,
            text_color=("#059669", "#10b981"),
            justify="right",
            wraplength=600
        )
        explanation_label.pack(pady=15)
        
        # إنشاء الأقسام الأربعة
        self.fields = {}
        
        # نقاط القوة
        self.create_swot_section(
            "strengths",
            "💪 نقاط القوة (Strengths)",
            "المهارات، القدرة المالية، القدرة الإدارية، الموارد البشرية، توفر المواد الأولية، الدعم العائلي، ...",
            "#2fa572"
        )
        
        # نقاط الضعف
        self.create_swot_section(
            "weaknesses",
            "⚠️ نقاط الضعف (Weaknesses)",
            "عدم وجود المهارات، ضعف القدرة المالية أو الإدارية، قلة الموارد البشرية، مشاكل السمعة، ...",
            "#ff6b35"
        )
        
        # الفرص
        self.create_swot_section(
            "opportunities",
            "🌟 الفرص (Opportunities)",
            "العوامل الاقتصادية، القانونية، الاجتماعية، السياسية، التكنولوجية، البيئية التي تصب في مصلحة المشروع",
            "#3b82f6"
        )
        
        # التهديدات
        self.create_swot_section(
            "threats",
            "⚡ التهديدات (Threats)",
            "عوامل خارجية قد تسبب ضررًا للمشروع، مثل: تقلب الاقتصاد، قوانين جديدة، تغيرات اجتماعية، منافسة شرسة، ...",
            "#8b5cf6"
        )
        
        # مصفوفة SWOT المرئية
        self.create_swot_matrix()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_swot_section(self, section_key, title, placeholder, color):
        """إنشاء قسم من أقسام SWOT"""
        # إطار القسم
        section_frame = ctk.CTkFrame(self.scrollable_frame)
        section_frame.pack(fill="x", padx=20, pady=10)
        
        # عنوان القسم
        section_title = ctk.CTkLabel(
            section_frame,
            text=title,
            font=self.arabic_font_bold,
            text_color=(color, color)
        )
        section_title.pack(pady=(15, 10))
        
        # منطقة النص
        textbox = ctk.CTkTextbox(
            section_frame,
            font=self.arabic_font,
            height=120,
            wrap="word",
            placeholder_text=placeholder
        )
        textbox.pack(fill="x", padx=20, pady=(0, 15))
        
        self.fields[section_key] = textbox
    
    def create_swot_matrix(self):
        """إنشاء مصفوفة SWOT المرئية"""
        matrix_frame = ctk.CTkFrame(self.scrollable_frame)
        matrix_frame.pack(fill="x", padx=20, pady=20)
        
        matrix_title = ctk.CTkLabel(
            matrix_frame,
            text="📊 مصفوفة SWOT المرئية",
            font=self.arabic_font_bold,
            text_color=("#1f538d", "#ffffff")
        )
        matrix_title.pack(pady=15)
        
        # إطار الجدول
        table_frame = ctk.CTkFrame(matrix_frame)
        table_frame.pack(padx=20, pady=10)
        
        # إنشاء الجدول
        # الصف الأول - العناوين
        header_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
        header_frame.pack(fill="x", padx=10, pady=5)
        
        # خلية فارغة
        empty_cell = ctk.CTkLabel(
            header_frame,
            text="",
            width=150,
            height=40
        )
        empty_cell.pack(side="right", padx=2)
        
        # عوامل خارجية
        external_header = ctk.CTkLabel(
            header_frame,
            text="عوامل خارجية",
            font=self.arabic_font_bold,
            width=300,
            height=40,
            fg_color=("#3b82f6", "#3b82f6"),
            corner_radius=5
        )
        external_header.pack(side="right", padx=2)
        
        # عوامل داخلية
        internal_header = ctk.CTkLabel(
            header_frame,
            text="عوامل داخلية",
            font=self.arabic_font_bold,
            width=300,
            height=40,
            fg_color=("#8b5cf6", "#8b5cf6"),
            corner_radius=5
        )
        internal_header.pack(side="right", padx=2)
        
        # الصف الثاني - الفرص والقوة
        positive_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
        positive_frame.pack(fill="x", padx=10, pady=2)
        
        positive_label = ctk.CTkLabel(
            positive_frame,
            text="إيجابية",
            font=self.arabic_font_bold,
            width=150,
            height=60,
            fg_color=("#2fa572", "#2fa572"),
            corner_radius=5
        )
        positive_label.pack(side="right", padx=2)
        
        opportunities_cell = ctk.CTkLabel(
            positive_frame,
            text="الفرص\n(Opportunities)",
            font=self.arabic_font,
            width=300,
            height=60,
            fg_color=("#e0f2fe", "#1e3a8a"),
            corner_radius=5
        )
        opportunities_cell.pack(side="right", padx=2)
        
        strengths_cell = ctk.CTkLabel(
            positive_frame,
            text="نقاط القوة\n(Strengths)",
            font=self.arabic_font,
            width=300,
            height=60,
            fg_color=("#f0fdf4", "#166534"),
            corner_radius=5
        )
        strengths_cell.pack(side="right", padx=2)
        
        # الصف الثالث - التهديدات والضعف
        negative_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
        negative_frame.pack(fill="x", padx=10, pady=2)
        
        negative_label = ctk.CTkLabel(
            negative_frame,
            text="سلبية",
            font=self.arabic_font_bold,
            width=150,
            height=60,
            fg_color=("#ff6b35", "#ff6b35"),
            corner_radius=5
        )
        negative_label.pack(side="right", padx=2)
        
        threats_cell = ctk.CTkLabel(
            negative_frame,
            text="التهديدات\n(Threats)",
            font=self.arabic_font,
            width=300,
            height=60,
            fg_color=("#fef2f2", "#991b1b"),
            corner_radius=5
        )
        threats_cell.pack(side="right", padx=2)
        
        weaknesses_cell = ctk.CTkLabel(
            negative_frame,
            text="نقاط الضعف\n(Weaknesses)",
            font=self.arabic_font,
            width=300,
            height=60,
            fg_color=("#fff7ed", "#c2410c"),
            corner_radius=5
        )
        weaknesses_cell.pack(side="right", padx=2)
        
        # نصائح استراتيجية
        tips_frame = ctk.CTkFrame(matrix_frame)
        tips_frame.pack(fill="x", padx=20, pady=10)
        
        tips_title = ctk.CTkLabel(
            tips_frame,
            text="💡 نصائح استراتيجية",
            font=self.arabic_font_bold
        )
        tips_title.pack(pady=(10, 5))
        
        tips_text = """
• استغل نقاط القوة لاستثمار الفرص المتاحة
• استخدم الفرص لتقوية نقاط الضعف
• واجه التهديدات بنقاط القوة المتوفرة
• تجنب المواقف التي تجمع بين نقاط الضعف والتهديدات
        """
        
        tips_label = ctk.CTkLabel(
            tips_frame,
            text=tips_text,
            font=self.arabic_font,
            justify="right",
            wraplength=600
        )
        tips_label.pack(pady=(0, 15))
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ التحليل الرباعي",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: المزيج التسويقي ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: دراسة السوق",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {}
            
            for field_key, textbox in self.fields.items():
                data[field_key] = textbox.get("1.0", tk.END).strip()
            
            self.data_manager.set_data("swot_analysis", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ التحليل الرباعي بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("swot_analysis")
            
            for field_key, textbox in self.fields.items():
                value = data.get(field_key, "")
                textbox.delete("1.0", tk.END)
                textbox.insert("1.0", value)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        for textbox in self.fields.values():
            textbox.delete("1.0", tk.END)
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("marketing_mix")
    
    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("market_analysis")
