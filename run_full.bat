@echo off
echo ===============================================
echo    تطبيق دراسة الجدوى الشامل - النسخة الكاملة
echo    Comprehensive Feasibility Study - Full Version
echo ===============================================
echo.

echo جاري تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo فشل في تثبيت المتطلبات، جاري تشغيل النسخة المبسطة...
    echo Failed to install requirements, running simple version...
    python main_simple.py
) else (
    echo.
    echo جاري تشغيل النسخة الكاملة...
    echo Starting Full Version...
    python main.py
)

if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل التطبيق!
    echo Error running the application!
    pause
)
