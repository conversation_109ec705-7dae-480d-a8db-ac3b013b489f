#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة وصف المشروع
Project Information Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
from datetime import datetime

class ProjectInfoFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="🧾 وصف المشروع")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="وصف المشروع والمعلومات الأساسية",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الجدول الأساسي
        basic_info_frame = ctk.CTkFrame(self.scrollable_frame)
        basic_info_frame.pack(fill="x", padx=20, pady=10)
        
        basic_title = ctk.CTkLabel(
            basic_info_frame,
            text="المعلومات الأساسية للمشروع",
            font=self.arabic_font_bold
        )
        basic_title.pack(pady=10)
        
        # إنشاء الحقول الأساسية
        self.fields = {}
        
        # الحقول النصية العادية
        text_fields = [
            ("project_name", "اسم المشروع"),
            ("project_location", "موقع المشروع"),
            ("grant_amount", "قيمة المنحة المطلوبة"),
            ("self_funding", "التمويل الذاتي أو مصادر التمويل الأخرى المتوفرة"),
            ("total_cost", "تكلفة المشروع الكلية")
        ]
        
        for field_key, field_label in text_fields:
            self.create_text_field(basic_info_frame, field_key, field_label)
        
        # حقل تاريخ تقديم خطة المشروع
        date_frame = ctk.CTkFrame(basic_info_frame, fg_color="transparent")
        date_frame.pack(fill="x", padx=10, pady=5)
        
        date_label = ctk.CTkLabel(
            date_frame,
            text="تاريخ تقديم خطة المشروع",
            font=self.arabic_font_bold,
            width=200,
            anchor="e"
        )
        date_label.pack(side="right", padx=(0, 20))
        
        self.fields["submission_date"] = ctk.CTkEntry(
            date_frame,
            font=self.arabic_font,
            width=300,
            justify="right",
            placeholder_text="مثال: 2024-01-15"
        )
        self.fields["submission_date"].pack(side="right", padx=20)
        
        # الحقول النصية الطويلة
        long_text_fields = [
            ("project_summary", "ملخص وصف خصائص المشروع"),
            ("project_characteristics", "خصائص المشروع"),
            ("description", "الوصف"),
            ("importance_description", "وصف أهمية الفكرة ونوع المشروع"),
            ("required_skills", "وصف المهارات اللازم امتلاكها لتنفيذ المشروع"),
            ("community_need", "وصف حاجة المجتمع للمشروع")
        ]
        
        for field_key, field_label in long_text_fields:
            self.create_text_area(self.scrollable_frame, field_key, field_label)
        
        # قسم الترخيص
        license_frame = ctk.CTkFrame(self.scrollable_frame)
        license_frame.pack(fill="x", padx=20, pady=10)
        
        license_title = ctk.CTkLabel(
            license_frame,
            text="معلومات الترخيص",
            font=self.arabic_font_bold
        )
        license_title.pack(pady=10)
        
        # سؤال الترخيص
        license_question_frame = ctk.CTkFrame(license_frame, fg_color="transparent")
        license_question_frame.pack(fill="x", padx=10, pady=5)
        
        license_question_label = ctk.CTkLabel(
            license_question_frame,
            text="هل يحتاج المشروع إلى ترخيص؟",
            font=self.arabic_font_bold,
            anchor="e"
        )
        license_question_label.pack(side="right", padx=(0, 20))
        
        self.license_var = tk.StringVar(value="لا")
        license_yes = ctk.CTkRadioButton(
            license_question_frame,
            text="نعم",
            font=self.arabic_font,
            variable=self.license_var,
            value="نعم",
            command=self.toggle_license_authority
        )
        license_yes.pack(side="right", padx=10)
        
        license_no = ctk.CTkRadioButton(
            license_question_frame,
            text="لا",
            font=self.arabic_font,
            variable=self.license_var,
            value="لا",
            command=self.toggle_license_authority
        )
        license_no.pack(side="right", padx=10)
        
        # حقل جهة الترخيص
        self.license_authority_frame = ctk.CTkFrame(license_frame, fg_color="transparent")
        self.license_authority_frame.pack(fill="x", padx=10, pady=5)
        
        authority_label = ctk.CTkLabel(
            self.license_authority_frame,
            text="جهة الترخيص",
            font=self.arabic_font_bold,
            width=200,
            anchor="e"
        )
        authority_label.pack(side="right", padx=(0, 20))
        
        self.fields["license_authority"] = ctk.CTkEntry(
            self.license_authority_frame,
            font=self.arabic_font,
            width=300,
            justify="right",
            state="disabled"
        )
        self.fields["license_authority"].pack(side="right", padx=20)
        
        # قسم الفئة المستهدفة
        target_frame = ctk.CTkFrame(self.scrollable_frame)
        target_frame.pack(fill="x", padx=20, pady=10)
        
        target_title = ctk.CTkLabel(
            target_frame,
            text="الفئة المستهدفة بالمشروع",
            font=self.arabic_font_bold
        )
        target_title.pack(pady=10)
        
        # خيارات الفئة المستهدفة
        self.target_audience_vars = {}
        target_options = [
            ("children", "أطفال"),
            ("youth", "شباب"),
            ("women", "نساء"),
            ("men", "رجال"),
            ("elderly", "كبار سن"),
            ("companies", "مؤسسات/شركات"),
            ("associations", "جمعيات"),
            ("schools", "مدارس"),
            ("hotels", "فنادق")
        ]
        
        target_grid_frame = ctk.CTkFrame(target_frame, fg_color="transparent")
        target_grid_frame.pack(fill="x", padx=20, pady=10)
        
        for i, (key, label) in enumerate(target_options):
            var = tk.BooleanVar()
            self.target_audience_vars[key] = var
            
            checkbox = ctk.CTkCheckBox(
                target_grid_frame,
                text=label,
                font=self.arabic_font,
                variable=var
            )
            
            row = i // 3
            col = 2 - (i % 3)  # RTL ordering
            checkbox.grid(row=row, column=col, padx=20, pady=5, sticky="e")
        
        # حقل "أخرى"
        other_frame = ctk.CTkFrame(target_frame, fg_color="transparent")
        other_frame.pack(fill="x", padx=10, pady=5)
        
        other_var = tk.BooleanVar()
        self.target_audience_vars["other"] = other_var
        
        other_checkbox = ctk.CTkCheckBox(
            other_frame,
            text="أخرى:",
            font=self.arabic_font,
            variable=other_var,
            command=self.toggle_other_target
        )
        other_checkbox.pack(side="right", padx=(0, 20))
        
        self.fields["other_target"] = ctk.CTkEntry(
            other_frame,
            font=self.arabic_font,
            width=200,
            justify="right",
            state="disabled"
        )
        self.fields["other_target"].pack(side="right", padx=10)
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_text_field(self, parent, field_key, field_label):
        """إنشاء حقل نص عادي"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", padx=10, pady=5)
        
        label = ctk.CTkLabel(
            row_frame,
            text=field_label,
            font=self.arabic_font_bold,
            width=200,
            anchor="e"
        )
        label.pack(side="right", padx=(0, 20))
        
        entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=300,
            justify="right"
        )
        entry.pack(side="right", padx=20)
        self.fields[field_key] = entry
    
    def create_text_area(self, parent, field_key, field_label):
        """إنشاء منطقة نص طويلة"""
        area_frame = ctk.CTkFrame(parent)
        area_frame.pack(fill="x", padx=20, pady=10)
        
        label = ctk.CTkLabel(
            area_frame,
            text=field_label,
            font=self.arabic_font_bold
        )
        label.pack(pady=(10, 5))
        
        textbox = ctk.CTkTextbox(
            area_frame,
            font=self.arabic_font,
            height=80,
            wrap="word"
        )
        textbox.pack(fill="x", padx=20, pady=(0, 10))
        self.fields[field_key] = textbox
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ وصف المشروع",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: دراسة السوق ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: المعلومات الشخصية",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)
    
    def toggle_license_authority(self):
        """تفعيل/إلغاء تفعيل حقل جهة الترخيص"""
        if self.license_var.get() == "نعم":
            self.fields["license_authority"].configure(state="normal")
        else:
            self.fields["license_authority"].configure(state="disabled")
            self.fields["license_authority"].delete(0, tk.END)
    
    def toggle_other_target(self):
        """تفعيل/إلغاء تفعيل حقل الفئة المستهدفة الأخرى"""
        if self.target_audience_vars["other"].get():
            self.fields["other_target"].configure(state="normal")
        else:
            self.fields["other_target"].configure(state="disabled")
            self.fields["other_target"].delete(0, tk.END)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {}
            
            # حفظ الحقول النصية
            for field_key, widget in self.fields.items():
                if isinstance(widget, ctk.CTkTextbox):
                    data[field_key] = widget.get("1.0", tk.END).strip()
                elif isinstance(widget, ctk.CTkEntry):
                    data[field_key] = widget.get()
            
            # حفظ معلومات الترخيص
            data["needs_license"] = self.license_var.get() == "نعم"
            
            # حفظ الفئة المستهدفة
            target_audience = []
            for key, var in self.target_audience_vars.items():
                if var.get():
                    if key == "other" and self.fields["other_target"].get():
                        target_audience.append(f"أخرى: {self.fields['other_target'].get()}")
                    elif key != "other":
                        # البحث عن التسمية العربية
                        target_options = {
                            "children": "أطفال", "youth": "شباب", "women": "نساء",
                            "men": "رجال", "elderly": "كبار سن", "companies": "مؤسسات/شركات",
                            "associations": "جمعيات", "schools": "مدارس", "hotels": "فنادق"
                        }
                        target_audience.append(target_options.get(key, key))
            
            data["target_audience"] = target_audience
            
            self.data_manager.set_data("project_info", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ وصف المشروع بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("project_info")
            
            # تحميل الحقول النصية
            for field_key, widget in self.fields.items():
                value = data.get(field_key, "")
                if isinstance(widget, ctk.CTkTextbox):
                    widget.delete("1.0", tk.END)
                    widget.insert("1.0", value)
                elif isinstance(widget, ctk.CTkEntry):
                    widget.delete(0, tk.END)
                    widget.insert(0, value)
            
            # تحميل معلومات الترخيص
            needs_license = data.get("needs_license", False)
            self.license_var.set("نعم" if needs_license else "لا")
            self.toggle_license_authority()
            
            # تحميل الفئة المستهدفة
            target_audience = data.get("target_audience", [])
            
            # إعادة تعيين جميع الخيارات
            for var in self.target_audience_vars.values():
                var.set(False)
            
            # تحديد الخيارات المحفوظة
            target_options_reverse = {
                "أطفال": "children", "شباب": "youth", "نساء": "women",
                "رجال": "men", "كبار سن": "elderly", "مؤسسات/شركات": "companies",
                "جمعيات": "associations", "مدارس": "schools", "فنادق": "hotels"
            }
            
            for target in target_audience:
                if target.startswith("أخرى:"):
                    self.target_audience_vars["other"].set(True)
                    self.fields["other_target"].delete(0, tk.END)
                    self.fields["other_target"].insert(0, target.replace("أخرى: ", ""))
                    self.toggle_other_target()
                elif target in target_options_reverse:
                    self.target_audience_vars[target_options_reverse[target]].set(True)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        for widget in self.fields.values():
            if isinstance(widget, ctk.CTkTextbox):
                widget.delete("1.0", tk.END)
            elif isinstance(widget, ctk.CTkEntry):
                widget.delete(0, tk.END)
        
        self.license_var.set("لا")
        self.toggle_license_authority()
        
        for var in self.target_audience_vars.values():
            var.set(False)
        self.toggle_other_target()
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("market_analysis")
    
    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("personal_info")
