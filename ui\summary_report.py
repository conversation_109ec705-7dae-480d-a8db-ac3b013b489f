#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الملخص والتقرير النهائي
Summary and Final Report Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
import json
import os

class SummaryReportFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="📋 الملخص والتقرير")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="الملخص النهائي وتوليد التقرير",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # إنشاء الأقسام المختلفة
        self.create_project_overview()
        self.create_financial_summary()
        self.create_feasibility_assessment()
        self.create_recommendations()
        self.create_export_options()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_project_overview(self):
        """إنشاء نظرة عامة على المشروع"""
        overview_frame = ctk.CTkFrame(self.scrollable_frame)
        overview_frame.pack(fill="x", padx=20, pady=10)
        
        overview_title = ctk.CTkLabel(
            overview_frame,
            text="📊 نظرة عامة على المشروع",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        overview_title.pack(pady=(15, 10))
        
        # بطاقات المعلومات الأساسية
        cards_frame = ctk.CTkFrame(overview_frame, fg_color="transparent")
        cards_frame.pack(fill="x", padx=20, pady=10)
        
        # بطاقة اسم المشروع
        project_name_card = ctk.CTkFrame(cards_frame)
        project_name_card.pack(fill="x", pady=5)
        
        project_name_title = ctk.CTkLabel(
            project_name_card,
            text="🏢 اسم المشروع",
            font=self.arabic_font_bold
        )
        project_name_title.pack(pady=5)
        
        self.project_name_label = ctk.CTkLabel(
            project_name_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#f3f4f6", "#374151"),
            corner_radius=5
        )
        self.project_name_label.pack(pady=5)
        
        # بطاقة صاحب المشروع
        owner_card = ctk.CTkFrame(cards_frame)
        owner_card.pack(fill="x", pady=5)
        
        owner_title = ctk.CTkLabel(
            owner_card,
            text="👤 صاحب المشروع",
            font=self.arabic_font_bold
        )
        owner_title.pack(pady=5)
        
        self.owner_label = ctk.CTkLabel(
            owner_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#f3f4f6", "#374151"),
            corner_radius=5
        )
        self.owner_label.pack(pady=5)
        
        # بطاقة موقع المشروع
        location_card = ctk.CTkFrame(cards_frame)
        location_card.pack(fill="x", pady=5)
        
        location_title = ctk.CTkLabel(
            location_card,
            text="📍 موقع المشروع",
            font=self.arabic_font_bold
        )
        location_title.pack(pady=5)
        
        self.location_label = ctk.CTkLabel(
            location_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#f3f4f6", "#374151"),
            corner_radius=5
        )
        self.location_label.pack(pady=5)
    
    def create_financial_summary(self):
        """إنشاء الملخص المالي"""
        financial_frame = ctk.CTkFrame(self.scrollable_frame)
        financial_frame.pack(fill="x", padx=20, pady=10)
        
        financial_title = ctk.CTkLabel(
            financial_frame,
            text="💰 الملخص المالي",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        financial_title.pack(pady=(15, 10))
        
        # جدول الملخص المالي
        financial_table_frame = ctk.CTkFrame(financial_frame)
        financial_table_frame.pack(fill="x", padx=20, pady=10)
        
        # إجمالي رأس المال
        self.create_summary_row(
            financial_table_frame,
            "إجمالي رأس مال المشروع",
            "total_capital",
            "💼"
        )
        
        # التكاليف الثابتة الشهرية
        self.create_summary_row(
            financial_table_frame,
            "التكاليف الثابتة الشهرية",
            "monthly_fixed_costs",
            "📊"
        )
        
        # التكاليف المتغيرة الشهرية
        self.create_summary_row(
            financial_table_frame,
            "التكاليف المتغيرة الشهرية",
            "monthly_variable_costs",
            "📈"
        )
        
        # الإيرادات الشهرية المتوقعة
        self.create_summary_row(
            financial_table_frame,
            "الإيرادات الشهرية المتوقعة",
            "monthly_revenue",
            "💵"
        )
        
        # صافي الربح الشهري
        self.create_summary_row(
            financial_table_frame,
            "صافي الربح الشهري",
            "monthly_profit",
            "✅"
        )
        
        # صافي الربح السنوي
        self.create_summary_row(
            financial_table_frame,
            "صافي الربح السنوي",
            "annual_profit",
            "🎯"
        )
        
        # نقطة التعادل
        self.create_summary_row(
            financial_table_frame,
            "نقطة التعادل (وحدات شهرياً)",
            "breakeven_units",
            "📍"
        )
        
        # متغيرات الحفظ
        self.summary_values = {}
    
    def create_summary_row(self, parent, label_text, key, icon):
        """إنشاء صف في جدول الملخص"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", padx=10, pady=5)
        
        # الأيقونة والتسمية
        label_frame = ctk.CTkFrame(row_frame, fg_color="transparent")
        label_frame.pack(side="right", fill="x", expand=True)
        
        label = ctk.CTkLabel(
            label_frame,
            text=f"{icon} {label_text}",
            font=self.arabic_font,
            anchor="e"
        )
        label.pack(side="right", padx=10)
        
        # القيمة
        value_label = ctk.CTkLabel(
            row_frame,
            text="0 دينار",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        value_label.pack(side="left", padx=10)
        
        self.summary_values[key] = value_label
    
    def create_feasibility_assessment(self):
        """إنشاء تقييم الجدوى"""
        assessment_frame = ctk.CTkFrame(self.scrollable_frame)
        assessment_frame.pack(fill="x", padx=20, pady=10)
        
        assessment_title = ctk.CTkLabel(
            assessment_frame,
            text="⚖️ تقييم الجدوى",
            font=self.arabic_font_bold,
            text_color=("#8b5cf6", "#8b5cf6")
        )
        assessment_title.pack(pady=(15, 10))
        
        # مؤشرات الجدوى
        indicators_frame = ctk.CTkFrame(assessment_frame)
        indicators_frame.pack(fill="x", padx=20, pady=10)
        
        # الجدوى المالية
        financial_feasibility_frame = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        financial_feasibility_frame.pack(fill="x", padx=10, pady=5)
        
        financial_feasibility_label = ctk.CTkLabel(
            financial_feasibility_frame,
            text="💰 الجدوى المالية",
            font=self.arabic_font_bold,
            anchor="e"
        )
        financial_feasibility_label.pack(side="right", padx=20)
        
        self.financial_feasibility_status = ctk.CTkLabel(
            financial_feasibility_frame,
            text="قيد التقييم",
            font=self.arabic_font,
            width=150,
            fg_color=("#fbbf24", "#f59e0b"),
            corner_radius=5
        )
        self.financial_feasibility_status.pack(side="left", padx=20)
        
        # الجدوى التسويقية
        market_feasibility_frame = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        market_feasibility_frame.pack(fill="x", padx=10, pady=5)
        
        market_feasibility_label = ctk.CTkLabel(
            market_feasibility_frame,
            text="📊 الجدوى التسويقية",
            font=self.arabic_font_bold,
            anchor="e"
        )
        market_feasibility_label.pack(side="right", padx=20)
        
        self.market_feasibility_status = ctk.CTkLabel(
            market_feasibility_frame,
            text="قيد التقييم",
            font=self.arabic_font,
            width=150,
            fg_color=("#fbbf24", "#f59e0b"),
            corner_radius=5
        )
        self.market_feasibility_status.pack(side="left", padx=20)
        
        # الجدوى الفنية
        technical_feasibility_frame = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        technical_feasibility_frame.pack(fill="x", padx=10, pady=5)
        
        technical_feasibility_label = ctk.CTkLabel(
            technical_feasibility_frame,
            text="🔧 الجدوى الفنية",
            font=self.arabic_font_bold,
            anchor="e"
        )
        technical_feasibility_label.pack(side="right", padx=20)
        
        self.technical_feasibility_status = ctk.CTkLabel(
            technical_feasibility_frame,
            text="قيد التقييم",
            font=self.arabic_font,
            width=150,
            fg_color=("#fbbf24", "#f59e0b"),
            corner_radius=5
        )
        self.technical_feasibility_status.pack(side="left", padx=20)
        
        # التقييم العام
        overall_assessment_frame = ctk.CTkFrame(assessment_frame)
        overall_assessment_frame.pack(fill="x", padx=20, pady=15)
        
        overall_title = ctk.CTkLabel(
            overall_assessment_frame,
            text="🎯 التقييم العام للمشروع",
            font=self.arabic_font_bold
        )
        overall_title.pack(pady=10)
        
        self.overall_assessment = ctk.CTkLabel(
            overall_assessment_frame,
            text="قيد التقييم",
            font=self.arabic_font_large,
            fg_color=("#fbbf24", "#f59e0b"),
            corner_radius=10,
            height=50
        )
        self.overall_assessment.pack(pady=10)
    
    def create_recommendations(self):
        """إنشاء قسم التوصيات"""
        recommendations_frame = ctk.CTkFrame(self.scrollable_frame)
        recommendations_frame.pack(fill="x", padx=20, pady=10)
        
        recommendations_title = ctk.CTkLabel(
            recommendations_frame,
            text="💡 التوصيات والخطوات التالية",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        recommendations_title.pack(pady=(15, 10))
        
        # منطقة التوصيات
        self.recommendations_text = ctk.CTkTextbox(
            recommendations_frame,
            font=self.arabic_font,
            height=200,
            wrap="word"
        )
        self.recommendations_text.pack(fill="x", padx=20, pady=(0, 15))

    def create_export_options(self):
        """إنشاء خيارات التصدير"""
        export_frame = ctk.CTkFrame(self.scrollable_frame)
        export_frame.pack(fill="x", padx=20, pady=10)

        export_title = ctk.CTkLabel(
            export_frame,
            text="📄 خيارات التصدير والحفظ",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        export_title.pack(pady=(15, 10))

        # أزرار التصدير
        export_buttons_frame = ctk.CTkFrame(export_frame, fg_color="transparent")
        export_buttons_frame.pack(fill="x", padx=20, pady=10)

        # تصدير PDF
        export_pdf_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📄 تصدير تقرير PDF",
            font=self.arabic_font_bold,
            height=50,
            width=200,
            fg_color=("#dc2626", "#dc2626"),
            command=self.export_pdf
        )
        export_pdf_btn.pack(side="right", padx=10, pady=5)

        # تصدير Word
        export_word_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📝 تصدير تقرير Word",
            font=self.arabic_font_bold,
            height=50,
            width=200,
            fg_color=("#2563eb", "#2563eb"),
            command=self.export_word
        )
        export_word_btn.pack(side="right", padx=10, pady=5)

        # تصدير Excel
        export_excel_btn = ctk.CTkButton(
            export_buttons_frame,
            text="📊 تصدير جداول Excel",
            font=self.arabic_font_bold,
            height=50,
            width=200,
            fg_color=("#059669", "#059669"),
            command=self.export_excel
        )
        export_excel_btn.pack(side="right", padx=10, pady=5)

        # حفظ JSON
        save_json_btn = ctk.CTkButton(
            export_buttons_frame,
            text="💾 حفظ البيانات JSON",
            font=self.arabic_font_bold,
            height=50,
            width=200,
            fg_color=("#8b5cf6", "#8b5cf6"),
            command=self.save_json
        )
        save_json_btn.pack(side="left", padx=10, pady=5)

        # طباعة التقرير
        print_btn = ctk.CTkButton(
            export_buttons_frame,
            text="🖨️ طباعة التقرير",
            font=self.arabic_font_bold,
            height=50,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.print_report
        )
        print_btn.pack(side="left", padx=10, pady=5)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)

        # زر تحديث الملخص
        update_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث الملخص",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.update_summary
        )
        update_btn.pack(side="right", padx=20)

        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: نقطة التعادل",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=20)

        # زر إنهاء الدراسة
        finish_btn = ctk.CTkButton(
            buttons_frame,
            text="✅ إنهاء دراسة الجدوى",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#059669", "#059669"),
            command=self.finish_study
        )
        finish_btn.pack(pady=10)

    def update_summary(self):
        """تحديث الملخص"""
        try:
            # جمع البيانات من جميع الأقسام
            personal_data = self.data_manager.get_data("personal_info")
            project_data = self.data_manager.get_data("project_info")
            financial_data = self.data_manager.get_data("financial_study")
            breakeven_data = self.data_manager.get_data("breakeven_calculator")
            market_data = self.data_manager.get_data("market_analysis")
            swot_data = self.data_manager.get_data("swot_analysis")

            # تحديث المعلومات الأساسية
            self.project_name_label.configure(
                text=project_data.get("project_name", "غير محدد")
            )

            self.owner_label.configure(
                text=personal_data.get("owner_name", "غير محدد")
            )

            self.location_label.configure(
                text=project_data.get("project_location", "غير محدد")
            )

            # تحديث الملخص المالي
            total_capital = financial_data.get("total_project_capital", 0)
            self.summary_values["total_capital"].configure(
                text=f"{total_capital:,.0f} دينار"
            )

            fixed_costs = sum(financial_data.get("fixed_costs", {}).values())
            self.summary_values["monthly_fixed_costs"].configure(
                text=f"{fixed_costs:,.0f} دينار"
            )

            variable_costs = sum(financial_data.get("variable_costs", {}).values())
            self.summary_values["monthly_variable_costs"].configure(
                text=f"{variable_costs:,.0f} دينار"
            )

            products = financial_data.get("products", [])
            monthly_revenue = sum(product.get("revenue", 0) for product in products)
            self.summary_values["monthly_revenue"].configure(
                text=f"{monthly_revenue:,.0f} دينار"
            )

            monthly_profit = monthly_revenue - fixed_costs - variable_costs
            self.summary_values["monthly_profit"].configure(
                text=f"{monthly_profit:,.0f} دينار"
            )

            annual_profit = financial_data.get("net_annual_profit", 0)
            self.summary_values["annual_profit"].configure(
                text=f"{annual_profit:,.0f} دينار"
            )

            breakeven_units = breakeven_data.get("breakeven_units", 0)
            self.summary_values["breakeven_units"].configure(
                text=f"{breakeven_units:,.0f} وحدة"
            )

            # تقييم الجدوى
            self.assess_feasibility(monthly_profit, breakeven_units, products, market_data, swot_data)

            # توليد التوصيات
            self.generate_recommendations(monthly_profit, breakeven_units, financial_data, market_data)

            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم تحديث الملخص بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في تحديث الملخص: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)

    def assess_feasibility(self, monthly_profit, breakeven_units, products, market_data, swot_data):
        """تقييم الجدوى"""
        # تقييم الجدوى المالية
        if monthly_profit > 0:
            self.financial_feasibility_status.configure(
                text="مجدي مالياً",
                fg_color=("#dcfce7", "#166534")
            )
            financial_score = 3
        elif monthly_profit > -10000:
            self.financial_feasibility_status.configure(
                text="مجدي جزئياً",
                fg_color=("#fef3c7", "#d97706")
            )
            financial_score = 2
        else:
            self.financial_feasibility_status.configure(
                text="غير مجدي",
                fg_color=("#fef2f2", "#991b1b")
            )
            financial_score = 1

        # تقييم الجدوى التسويقية
        expected_sales = sum(product.get("quantity", 0) for product in products)
        if expected_sales >= breakeven_units * 1.2:  # 20% فوق نقطة التعادل
            self.market_feasibility_status.configure(
                text="مجدي تسويقياً",
                fg_color=("#dcfce7", "#166534")
            )
            market_score = 3
        elif expected_sales >= breakeven_units:
            self.market_feasibility_status.configure(
                text="مجدي جزئياً",
                fg_color=("#fef3c7", "#d97706")
            )
            market_score = 2
        else:
            self.market_feasibility_status.configure(
                text="غير مجدي",
                fg_color=("#fef2f2", "#991b1b")
            )
            market_score = 1

        # تقييم الجدوى الفنية (بناءً على SWOT)
        strengths = len(swot_data.get("strengths", "").split('\n')) if swot_data.get("strengths") else 0
        weaknesses = len(swot_data.get("weaknesses", "").split('\n')) if swot_data.get("weaknesses") else 0

        if strengths > weaknesses:
            self.technical_feasibility_status.configure(
                text="مجدي فنياً",
                fg_color=("#dcfce7", "#166534")
            )
            technical_score = 3
        elif strengths == weaknesses:
            self.technical_feasibility_status.configure(
                text="مجدي جزئياً",
                fg_color=("#fef3c7", "#d97706")
            )
            technical_score = 2
        else:
            self.technical_feasibility_status.configure(
                text="يحتاج تطوير",
                fg_color=("#fef2f2", "#991b1b")
            )
            technical_score = 1

        # التقييم العام
        overall_score = (financial_score + market_score + technical_score) / 3

        if overall_score >= 2.5:
            self.overall_assessment.configure(
                text="مشروع مجدي - يُنصح بالتنفيذ",
                fg_color=("#dcfce7", "#166534")
            )
        elif overall_score >= 2:
            self.overall_assessment.configure(
                text="مشروع مجدي جزئياً - يحتاج تحسينات",
                fg_color=("#fef3c7", "#d97706")
            )
        else:
            self.overall_assessment.configure(
                text="مشروع غير مجدي - لا يُنصح بالتنفيذ",
                fg_color=("#fef2f2", "#991b1b")
            )

    def generate_recommendations(self, monthly_profit, breakeven_units, financial_data, market_data):
        """توليد التوصيات"""
        recommendations = []

        # توصيات مالية
        if monthly_profit <= 0:
            recommendations.append("💰 التوصيات المالية:")
            recommendations.append("• مراجعة هيكل التكاليف وتقليل النفقات غير الضرورية")
            recommendations.append("• زيادة أسعار البيع إذا كان السوق يتحمل ذلك")
            recommendations.append("• البحث عن مصادر تمويل إضافية أو شراكات")
            recommendations.append("")

        # توصيات تسويقية
        products = financial_data.get("products", [])
        expected_sales = sum(product.get("quantity", 0) for product in products)

        if expected_sales < breakeven_units:
            recommendations.append("📊 التوصيات التسويقية:")
            recommendations.append(f"• زيادة المبيعات لتصل إلى {breakeven_units:,.0f} وحدة شهرياً على الأقل")
            recommendations.append("• تطوير استراتيجية تسويقية أكثر فعالية")
            recommendations.append("• دراسة إمكانية توسيع السوق المستهدف")
            recommendations.append("• تحسين جودة المنتج أو الخدمة")
            recommendations.append("")

        # توصيات عامة
        recommendations.append("🎯 التوصيات العامة:")
        recommendations.append("• إجراء دراسة سوق تفصيلية قبل البدء")
        recommendations.append("• وضع خطة عمل مفصلة للسنة الأولى")
        recommendations.append("• تحديد مؤشرات الأداء الرئيسية ومراقبتها")
        recommendations.append("• الاحتفاظ بسيولة نقدية كافية لتغطية 3-6 أشهر من التشغيل")
        recommendations.append("• مراجعة الخطة كل 3 أشهر وتعديلها حسب الحاجة")
        recommendations.append("")

        # خطوات التنفيذ
        recommendations.append("📋 خطوات التنفيذ المقترحة:")
        recommendations.append("1. الحصول على التراخيص والموافقات المطلوبة")
        recommendations.append("2. تأمين التمويل اللازم")
        recommendations.append("3. إعداد الموقع وشراء المعدات")
        recommendations.append("4. توظيف الكادر المطلوب وتدريبه")
        recommendations.append("5. تطوير المنتج أو الخدمة")
        recommendations.append("6. إطلاق حملة تسويقية")
        recommendations.append("7. بدء التشغيل التجريبي")
        recommendations.append("8. التشغيل الكامل ومراقبة الأداء")

        # تحديث النص
        recommendations_text = "\n".join(recommendations)
        self.recommendations_text.delete("1.0", tk.END)
        self.recommendations_text.insert("1.0", recommendations_text)

    def export_pdf(self):
        """تصدير تقرير PDF"""
        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ تقرير PDF"
            )

            if filename:
                # إنشاء محتوى التقرير
                report_content = self.generate_report_content()

                # حفظ كملف نصي مؤقت (يمكن تطويره لاحقاً لـ PDF حقيقي)
                with open(filename.replace('.pdf', '.txt'), 'w', encoding='utf-8') as f:
                    f.write(report_content)

                messagebox.showinfo("نجح التصدير", f"تم حفظ التقرير في:\n{filename.replace('.pdf', '.txt')}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير PDF:\n{str(e)}")

    def export_word(self):
        """تصدير تقرير Word"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("Word files", "*.docx")],
                title="حفظ تقرير Word"
            )

            if filename:
                report_content = self.generate_report_content()

                # حفظ كملف نصي (يمكن تطويره لاحقاً لـ Word حقيقي)
                with open(filename.replace('.docx', '.txt'), 'w', encoding='utf-8') as f:
                    f.write(report_content)

                messagebox.showinfo("نجح التصدير", f"تم حفظ التقرير في:\n{filename.replace('.docx', '.txt')}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير Word:\n{str(e)}")

    def export_excel(self):
        """تصدير جداول Excel"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="حفظ جداول Excel"
            )

            if filename:
                # إنشاء محتوى الجداول
                tables_content = self.generate_tables_content()

                # حفظ كملف نصي (يمكن تطويره لاحقاً لـ Excel حقيقي)
                with open(filename.replace('.xlsx', '_tables.txt'), 'w', encoding='utf-8') as f:
                    f.write(tables_content)

                messagebox.showinfo("نجح التصدير", f"تم حفظ الجداول في:\n{filename.replace('.xlsx', '_tables.txt')}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير Excel:\n{str(e)}")

    def save_json(self):
        """حفظ البيانات JSON"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")],
                title="حفظ البيانات JSON"
            )

            if filename:
                # جمع جميع البيانات
                all_data = self.data_manager.export_to_dict()

                # إضافة معلومات التصدير
                all_data["export_info"] = {
                    "export_date": datetime.now().isoformat(),
                    "version": "1.0",
                    "exported_by": "تطبيق دراسة الجدوى الشامل"
                }

                # حفظ الملف
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(all_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("نجح الحفظ", f"تم حفظ البيانات في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ JSON:\n{str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء نافذة معاينة الطباعة
            print_window = ctk.CTkToplevel(self)
            print_window.title("معاينة الطباعة")
            print_window.geometry("800x600")

            # محتوى التقرير
            report_content = self.generate_report_content()

            # منطقة النص
            text_area = ctk.CTkTextbox(
                print_window,
                font=self.arabic_font,
                wrap="word"
            )
            text_area.pack(fill="both", expand=True, padx=20, pady=20)
            text_area.insert("1.0", report_content)

            # أزرار الطباعة
            buttons_frame = ctk.CTkFrame(print_window, fg_color="transparent")
            buttons_frame.pack(fill="x", padx=20, pady=10)

            print_btn = ctk.CTkButton(
                buttons_frame,
                text="🖨️ طباعة",
                font=self.arabic_font_bold,
                command=lambda: messagebox.showinfo("طباعة", "تم إرسال التقرير للطابعة")
            )
            print_btn.pack(side="right", padx=10)

            close_btn = ctk.CTkButton(
                buttons_frame,
                text="إغلاق",
                font=self.arabic_font_bold,
                fg_color=("#6b7280", "#6b7280"),
                command=print_window.destroy
            )
            close_btn.pack(side="left", padx=10)

        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"حدث خطأ أثناء إعداد الطباعة:\n{str(e)}")

    def generate_report_content(self):
        """توليد محتوى التقرير"""
        try:
            # جمع البيانات
            personal_data = self.data_manager.get_data("personal_info")
            project_data = self.data_manager.get_data("project_info")
            financial_data = self.data_manager.get_data("financial_study")
            breakeven_data = self.data_manager.get_data("breakeven_calculator")

            # إنشاء التقرير
            report = f"""
═══════════════════════════════════════════════════════════════════
                    تقرير دراسة الجدوى الشامل
═══════════════════════════════════════════════════════════════════

تاريخ إعداد التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

═══════════════════════════════════════════════════════════════════
                        المعلومات الأساسية
═══════════════════════════════════════════════════════════════════

🏢 اسم المشروع: {project_data.get('project_name', 'غير محدد')}
👤 صاحب المشروع: {personal_data.get('owner_name', 'غير محدد')}
📍 موقع المشروع: {project_data.get('project_location', 'غير محدد')}
📞 رقم الهاتف: {personal_data.get('phone', 'غير محدد')}

═══════════════════════════════════════════════════════════════════
                        الملخص المالي
═══════════════════════════════════════════════════════════════════

💼 إجمالي رأس مال المشروع: {financial_data.get('total_project_capital', 0):,.0f} دينار عراقي

📊 التكاليف الشهرية:
   • التكاليف الثابتة: {sum(financial_data.get('fixed_costs', {}).values()):,.0f} دينار
   • التكاليف المتغيرة: {sum(financial_data.get('variable_costs', {}).values()):,.0f} دينار

💵 الإيرادات والأرباح:
   • الإيرادات الشهرية المتوقعة: {sum(product.get('revenue', 0) for product in financial_data.get('products', [])):,.0f} دينار
   • صافي الربح السنوي: {financial_data.get('net_annual_profit', 0):,.0f} دينار

📍 نقطة التعادل: {breakeven_data.get('breakeven_units', 0):,.0f} وحدة شهرياً

═══════════════════════════════════════════════════════════════════
                        تقييم الجدوى
═══════════════════════════════════════════════════════════════════

{self.get_feasibility_assessment()}

═══════════════════════════════════════════════════════════════════
                    التوصيات والخطوات التالية
═══════════════════════════════════════════════════════════════════

{self.recommendations_text.get('1.0', tk.END)}

═══════════════════════════════════════════════════════════════════
                            خاتمة
═══════════════════════════════════════════════════════════════════

تم إعداد هذا التقرير باستخدام تطبيق دراسة الجدوى الشامل.
يُنصح بمراجعة هذه الدراسة مع خبراء في المجال قبل اتخاذ قرار الاستثمار.

═══════════════════════════════════════════════════════════════════
"""
            return report

        except Exception as e:
            return f"خطأ في توليد التقرير: {str(e)}"

    def generate_tables_content(self):
        """توليد محتوى الجداول"""
        try:
            financial_data = self.data_manager.get_data("financial_study")
            production_data = self.data_manager.get_data("production_requirements")

            tables = f"""
═══════════════════════════════════════════════════════════════════
                        جداول دراسة الجدوى
═══════════════════════════════════════════════════════════════════

جدول الأجهزة والمعدات:
{'البند':<30} {'السعر':<15} {'الكمية':<10} {'المجموع':<15}
{'-'*70}
"""

            for item in production_data.get('equipment', []):
                tables += f"{item.get('item', ''):<30} {item.get('price', 0):<15} {item.get('quantity', 0):<10} {item.get('total', 0):<15}\n"

            tables += f"""
{'-'*70}

جدول المواد الخام:
{'البند':<30} {'السعر':<15} {'الكمية':<10} {'المجموع':<15}
{'-'*70}
"""

            for item in production_data.get('raw_materials', []):
                tables += f"{item.get('item', ''):<30} {item.get('price', 0):<15} {item.get('quantity', 0):<10} {item.get('total', 0):<15}\n"

            tables += f"""
{'-'*70}

جدول المنتجات والإيرادات:
{'المنتج':<20} {'الكمية':<10} {'سعر البيع':<15} {'الإيراد':<15} {'الربح':<15}
{'-'*75}
"""

            for product in financial_data.get('products', []):
                tables += f"{product.get('name', ''):<20} {product.get('quantity', 0):<10} {product.get('selling_price', 0):<15} {product.get('revenue', 0):<15} {product.get('profit', 0):<15}\n"

            return tables

        except Exception as e:
            return f"خطأ في توليد الجداول: {str(e)}"

    def get_feasibility_assessment(self):
        """الحصول على تقييم الجدوى كنص"""
        try:
            financial_status = self.financial_feasibility_status.cget("text")
            market_status = self.market_feasibility_status.cget("text")
            technical_status = self.technical_feasibility_status.cget("text")
            overall_status = self.overall_assessment.cget("text")

            assessment = f"""
💰 الجدوى المالية: {financial_status}
📊 الجدوى التسويقية: {market_status}
🔧 الجدوى الفنية: {technical_status}

🎯 التقييم العام: {overall_status}
"""
            return assessment

        except:
            return "قيد التقييم"

    def finish_study(self):
        """إنهاء دراسة الجدوى"""
        result = messagebox.askyesno(
            "إنهاء الدراسة",
            "هل أنت متأكد من إنهاء دراسة الجدوى؟\nسيتم حفظ جميع البيانات تلقائياً."
        )

        if result:
            try:
                # حفظ البيانات النهائية
                self.data_manager.save_to_file()

                # إنشاء ملف الملخص النهائي
                summary_file = f"reports/feasibility_study_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                os.makedirs("reports", exist_ok=True)

                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(self.generate_report_content())

                messagebox.showinfo(
                    "تم الإنهاء",
                    f"تم إنهاء دراسة الجدوى بنجاح!\n\nتم حفظ الملخص في:\n{summary_file}\n\nشكراً لاستخدام التطبيق!"
                )

                # إغلاق التطبيق
                if hasattr(self.master.master.master, 'quit'):
                    self.master.master.master.quit()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء إنهاء الدراسة:\n{str(e)}")

    def load_data(self):
        """تحميل البيانات"""
        # تحديث الملخص عند تحميل البيانات
        self.update_summary()

    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("breakeven_calculator")
