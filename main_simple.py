#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق دراسة الجدوى الشامل - نسخة مبسطة
Comprehensive Feasibility Study Application - Simple Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime

class FeasibilityStudyApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("تطبيق دراسة الجدوى الشامل - Comprehensive Feasibility Study")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f0f0f0")
        
        # البيانات
        self.data = {
            "personal_info": {},
            "project_info": {},
            "market_analysis": {},
            "swot_analysis": {},
            "marketing_mix": {},
            "production_requirements": {},
            "financial_data": {},
            "breakeven_data": {}
        }
        
        # إعداد الواجهة
        self.setup_interface()
        
    def setup_interface(self):
        """إعداد الواجهة الرئيسية"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg="#f0f0f0")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان التطبيق
        title_label = tk.Label(
            main_frame,
            text="تطبيق دراسة الجدوى الشامل",
            font=("Arial", 18, "bold"),
            bg="#f0f0f0",
            fg="#1f538d"
        )
        title_label.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        # أزرار الأقسام
        sections = [
            ("المعلومات الشخصية", self.open_personal_info),
            ("وصف المشروع", self.open_project_info),
            ("دراسة السوق والمنافسين", self.open_market_analysis),
            ("التحليل الرباعي SWOT", self.open_swot_analysis),
            ("المزيج التسويقي", self.open_marketing_mix),
            ("مستلزمات الإنتاج", self.open_production_requirements),
            ("الدراسة المالية", self.open_financial_study),
            ("نقطة التعادل", self.open_breakeven_calculator),
            ("الملخص والتقرير", self.open_summary_report)
        ]
        
        for i, (section_name, command) in enumerate(sections):
            btn = tk.Button(
                buttons_frame,
                text=section_name,
                font=("Arial", 10),
                width=25,
                height=2,
                bg="#3b82f6",
                fg="white",
                command=command
            )
            btn.grid(row=i//3, column=i%3, padx=5, pady=5)
        
        # إطار الحفظ والتحميل
        save_frame = tk.Frame(main_frame, bg="#f0f0f0")
        save_frame.pack(pady=20)
        
        save_btn = tk.Button(
            save_frame,
            text="💾 حفظ البيانات",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=self.save_data
        )
        save_btn.pack(side="left", padx=10)
        
        load_btn = tk.Button(
            save_frame,
            text="📂 تحميل البيانات",
            font=("Arial", 12),
            bg="#ff6b35",
            fg="white",
            command=self.load_data
        )
        load_btn.pack(side="left", padx=10)
        
        # معلومات التطبيق
        info_label = tk.Label(
            main_frame,
            text="تطبيق شامل لإعداد دراسة الجدوى مع حساب نقطة التعادل وتوليد التقارير",
            font=("Arial", 10),
            bg="#f0f0f0",
            fg="#6b7280"
        )
        info_label.pack(pady=20)
    
    def open_personal_info(self):
        """فتح نافذة المعلومات الشخصية"""
        window = tk.Toplevel(self.root)
        window.title("المعلومات الشخصية")
        window.geometry("600x500")
        window.configure(bg="#ffffff")
        
        # عنوان
        title = tk.Label(
            window,
            text="المعلومات الشخصية لصاحب المشروع",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)
        
        # الحقول
        fields_frame = tk.Frame(window, bg="#ffffff")
        fields_frame.pack(pady=20, padx=40, fill="both", expand=True)
        
        fields = [
            ("اسم صاحب/ة المشروع", "owner_name"),
            ("العمر", "age"),
            ("الحالة الاجتماعية", "marital_status"),
            ("عدد أفراد الأسرة", "family_members"),
            ("المؤهل العلمي", "education"),
            ("رقم الهاتف", "phone"),
            ("رقم هاتف شخص معرف", "reference_phone"),
            ("مكان السكن", "address")
        ]
        
        entries = {}
        
        for i, (label_text, field_key) in enumerate(fields):
            # التسمية
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=("Arial", 12),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i, column=0, sticky="e", padx=10, pady=10)
            
            # حقل الإدخال
            entry = tk.Entry(
                fields_frame,
                font=("Arial", 12),
                width=30,
                justify="right"
            )
            entry.grid(row=i, column=1, padx=10, pady=10)
            entries[field_key] = entry
            
            # تحميل البيانات المحفوظة
            if field_key in self.data["personal_info"]:
                entry.insert(0, self.data["personal_info"][field_key])
        
        # زر الحفظ
        save_btn = tk.Button(
            window,
            text="حفظ المعلومات",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_personal_info(entries, window)
        )
        save_btn.pack(pady=20)
    
    def save_personal_info(self, entries, window):
        """حفظ المعلومات الشخصية"""
        for field_key, entry in entries.items():
            self.data["personal_info"][field_key] = entry.get()
        
        messagebox.showinfo("نجح الحفظ", "تم حفظ المعلومات الشخصية بنجاح!")
        window.destroy()
    
    def open_project_info(self):
        """فتح نافذة وصف المشروع"""
        window = tk.Toplevel(self.root)
        window.title("وصف المشروع")
        window.geometry("700x600")
        window.configure(bg="#ffffff")
        
        # عنوان
        title = tk.Label(
            window,
            text="وصف المشروع والمعلومات الأساسية",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الحقول
        fields = [
            ("اسم المشروع", "project_name"),
            ("موقع المشروع", "project_location"),
            ("قيمة المنحة المطلوبة", "grant_amount"),
            ("التمويل الذاتي أو مصادر التمويل الأخرى المتوفرة", "self_funding"),
            ("تكلفة المشروع الكلية", "total_cost"),
            ("تاريخ تقديم خطة المشروع", "submission_date")
        ]
        
        entries = {}
        
        for i, (label_text, field_key) in enumerate(fields):
            # التسمية
            label = tk.Label(
                scrollable_frame,
                text=label_text,
                font=("Arial", 12),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i, column=0, sticky="e", padx=10, pady=10)
            
            # حقل الإدخال
            entry = tk.Entry(
                scrollable_frame,
                font=("Arial", 12),
                width=30,
                justify="right"
            )
            entry.grid(row=i, column=1, padx=10, pady=10)
            entries[field_key] = entry
            
            # تحميل البيانات المحفوظة
            if field_key in self.data["project_info"]:
                entry.insert(0, self.data["project_info"][field_key])
        
        # الحقول النصية الطويلة
        text_fields = [
            ("ملخص وصف خصائص المشروع", "project_summary"),
            ("خصائص المشروع", "project_characteristics"),
            ("الوصف التفصيلي", "description"),
            ("وصف أهمية الفكرة ونوع المشروع", "importance_description"),
            ("وصف المهارات اللازم امتلاكها لتنفيذ المشروع", "required_skills"),
            ("وصف حاجة المجتمع للمشروع", "community_need")
        ]

        text_entries = {}
        current_row = len(fields)

        for field_label, field_key in text_fields:
            # التسمية
            label = tk.Label(
                scrollable_frame,
                text=field_label,
                font=("Arial", 12, "bold"),
                bg="#ffffff"
            )
            label.grid(row=current_row, column=0, columnspan=2, pady=(15, 5))

            # منطقة النص
            text_widget = tk.Text(
                scrollable_frame,
                font=("Arial", 11),
                width=60,
                height=4,
                wrap="word"
            )
            text_widget.grid(row=current_row+1, column=0, columnspan=2, padx=10, pady=5)
            text_entries[field_key] = text_widget

            # تحميل البيانات المحفوظة
            if field_key in self.data["project_info"]:
                text_widget.insert("1.0", self.data["project_info"][field_key])

            current_row += 2
        
        # زر الحفظ
        save_btn = tk.Button(
            scrollable_frame,
            text="حفظ وصف المشروع",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_project_info(entries, text_entries, window)
        )
        save_btn.grid(row=current_row, column=0, columnspan=2, pady=20)
    
    def save_project_info(self, entries, text_entries, window):
        """حفظ وصف المشروع"""
        # حفظ الحقول النصية العادية
        for field_key, entry in entries.items():
            self.data["project_info"][field_key] = entry.get()

        # حفظ الحقول النصية الطويلة
        for field_key, text_widget in text_entries.items():
            self.data["project_info"][field_key] = text_widget.get("1.0", tk.END).strip()

        messagebox.showinfo("نجح الحفظ", "تم حفظ وصف المشروع بنجاح!")
        window.destroy()

    def open_market_analysis(self):
        """فتح نافذة دراسة السوق والمنافسين"""
        window = tk.Toplevel(self.root)
        window.title("دراسة السوق والمنافسين")
        window.geometry("800x700")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="دراسة السوق والمنافسين",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # الحقول
        market_fields = [
            ("ما هي المنتجات التي ستقدمها؟", "products_offered"),
            ("ما هي الخدمات التي ستقدمها؟", "services_offered"),
            ("كم عدد المنافسين؟", "competitors_count"),
            ("ما هي المنتجات المنافسة أو الشبيهة؟", "competing_products"),
            ("كم أعداد الزبائن المحتمل أن يشتروا منتجاتك؟", "potential_customers"),
            ("كم معدل استهلاكهم للمنتج في الشهر؟", "monthly_consumption"),
            ("ما هي المواسم التي يشتد فيها البيع؟", "peak_seasons"),
            ("ما هي ميزتك التنافسية؟", "competitive_advantage"),
            ("ما هي استراتيجيتك التسويقية؟", "marketing_strategy")
        ]

        market_entries = {}

        for i, (label_text, field_key) in enumerate(market_fields):
            # التسمية
            label = tk.Label(
                scrollable_frame,
                text=label_text,
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i*2, column=0, columnspan=2, sticky="e", padx=10, pady=(10, 5))

            # حقل الإدخال
            if field_key in ["products_offered", "services_offered", "competing_products", "competitive_advantage", "marketing_strategy"]:
                # حقول نصية طويلة
                entry = tk.Text(
                    scrollable_frame,
                    font=("Arial", 11),
                    width=60,
                    height=3,
                    wrap="word"
                )
                entry.grid(row=i*2+1, column=0, columnspan=2, padx=10, pady=5)

                # تحميل البيانات المحفوظة
                if "market_analysis" in self.data and field_key in self.data["market_analysis"]:
                    entry.insert("1.0", self.data["market_analysis"][field_key])
            else:
                # حقول نصية عادية
                entry = tk.Entry(
                    scrollable_frame,
                    font=("Arial", 12),
                    width=50,
                    justify="right"
                )
                entry.grid(row=i*2+1, column=0, columnspan=2, padx=10, pady=5)

                # تحميل البيانات المحفوظة
                if "market_analysis" in self.data and field_key in self.data["market_analysis"]:
                    entry.insert(0, self.data["market_analysis"][field_key])

            market_entries[field_key] = entry

        # زر الحفظ
        save_btn = tk.Button(
            scrollable_frame,
            text="حفظ دراسة السوق",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_market_analysis(market_entries, window)
        )
        save_btn.grid(row=len(market_fields)*2, column=0, columnspan=2, pady=20)

    def save_market_analysis(self, entries, window):
        """حفظ دراسة السوق"""
        if "market_analysis" not in self.data:
            self.data["market_analysis"] = {}

        for field_key, widget in entries.items():
            if isinstance(widget, tk.Text):
                self.data["market_analysis"][field_key] = widget.get("1.0", tk.END).strip()
            else:
                self.data["market_analysis"][field_key] = widget.get()

        messagebox.showinfo("نجح الحفظ", "تم حفظ دراسة السوق بنجاح!")
        window.destroy()

    def open_swot_analysis(self):
        """فتح نافذة التحليل الرباعي SWOT"""
        window = tk.Toplevel(self.root)
        window.title("التحليل الرباعي SWOT")
        window.geometry("700x600")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="التحليل الرباعي - SWOT Analysis",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # شرح
        explanation = tk.Label(
            window,
            text="التحليل الرباعي يساعد في تقييم نقاط القوة والضعف والفرص والتهديدات",
            font=("Arial", 12),
            bg="#ffffff",
            fg="#6b7280"
        )
        explanation.pack(pady=10)

        # إطار الأقسام الأربعة
        main_frame = tk.Frame(window, bg="#ffffff")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # الأقسام الأربعة
        swot_sections = [
            ("نقاط القوة (Strengths)", "strengths", "#2fa572"),
            ("نقاط الضعف (Weaknesses)", "weaknesses", "#ff6b35"),
            ("الفرص (Opportunities)", "opportunities", "#3b82f6"),
            ("التهديدات (Threats)", "threats", "#8b5cf6")
        ]

        swot_entries = {}

        for i, (section_name, section_key, color) in enumerate(swot_sections):
            # إطار القسم
            section_frame = tk.LabelFrame(
                main_frame,
                text=section_name,
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                fg=color
            )
            section_frame.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="nsew")

            # منطقة النص
            text_widget = tk.Text(
                section_frame,
                font=("Arial", 11),
                width=30,
                height=8,
                wrap="word"
            )
            text_widget.pack(padx=10, pady=10, fill="both", expand=True)

            # تحميل البيانات المحفوظة
            if "swot_analysis" in self.data and section_key in self.data["swot_analysis"]:
                text_widget.insert("1.0", self.data["swot_analysis"][section_key])

            swot_entries[section_key] = text_widget

        # تكوين الشبكة
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)

        # زر الحفظ
        save_btn = tk.Button(
            window,
            text="حفظ التحليل الرباعي",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_swot_analysis(swot_entries, window)
        )
        save_btn.pack(pady=20)

    def save_swot_analysis(self, entries, window):
        """حفظ التحليل الرباعي"""
        if "swot_analysis" not in self.data:
            self.data["swot_analysis"] = {}

        for section_key, text_widget in entries.items():
            self.data["swot_analysis"][section_key] = text_widget.get("1.0", tk.END).strip()

        messagebox.showinfo("نجح الحفظ", "تم حفظ التحليل الرباعي بنجاح!")
        window.destroy()

    def open_marketing_mix(self):
        """فتح نافذة المزيج التسويقي"""
        window = tk.Toplevel(self.root)
        window.title("المزيج التسويقي - Marketing Mix")
        window.geometry("800x700")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="عناصر المزيج التسويقي (4Ps + 1)",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عناصر المزيج التسويقي
        marketing_mix_fields = [
            # المنتج (Product)
            ("المنتج - ما هي المنتجات التي ستقدمها؟", "product_products"),
            ("المنتج - هل فيها تنوع؟", "product_variety"),
            ("المنتج - هل جودتها عالية؟", "product_quality"),
            ("المنتج - هل ستقدم خدمات مصاحبة؟", "product_services"),

            # السعر (Price)
            ("السعر - ما هي أسعار البيع؟", "price_selling_prices"),
            ("السعر - هل هناك أسعار جملة وتجزئة؟", "price_wholesale_retail"),
            ("السعر - هل ستقدم تخفيضات أو خصومات؟", "price_discounts"),

            # المكان (Place)
            ("المكان - كيف ستوزع المنتجات؟", "place_distribution"),
            ("المكان - ما هي قنوات البيع والتوزيع؟", "place_sales_channels"),
            ("المكان - هل يوجد توصيل ونقل؟", "place_delivery"),

            # الترويج (Promotion)
            ("الترويج - كيف ستروج للمشروع؟", "promotion_strategy"),
            ("الترويج - الإعلان", "promotion_advertising"),
            ("الترويج - وسائل التواصل الاجتماعي", "promotion_social_media"),

            # الناس (People)
            ("الناس - من سيساعدك في المشروع؟", "people_helpers"),
            ("الناس - موظفين", "people_employees"),
            ("الناس - أفراد من العائلة", "people_family")
        ]

        marketing_entries = {}

        for i, (label_text, field_key) in enumerate(marketing_mix_fields):
            # التسمية
            label = tk.Label(
                scrollable_frame,
                text=label_text,
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i*2, column=0, columnspan=2, sticky="e", padx=10, pady=(10, 5))

            # حقل الإدخال
            entry = tk.Entry(
                scrollable_frame,
                font=("Arial", 11),
                width=60,
                justify="right"
            )
            entry.grid(row=i*2+1, column=0, columnspan=2, padx=10, pady=5)

            # تحميل البيانات المحفوظة
            if "marketing_mix" in self.data and field_key in self.data["marketing_mix"]:
                entry.insert(0, self.data["marketing_mix"][field_key])

            marketing_entries[field_key] = entry

        # زر الحفظ
        save_btn = tk.Button(
            scrollable_frame,
            text="حفظ المزيج التسويقي",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_marketing_mix(marketing_entries, window)
        )
        save_btn.grid(row=len(marketing_mix_fields)*2, column=0, columnspan=2, pady=20)

    def save_marketing_mix(self, entries, window):
        """حفظ المزيج التسويقي"""
        if "marketing_mix" not in self.data:
            self.data["marketing_mix"] = {}

        for field_key, entry in entries.items():
            self.data["marketing_mix"][field_key] = entry.get()

        messagebox.showinfo("نجح الحفظ", "تم حفظ المزيج التسويقي بنجاح!")
        window.destroy()

    def open_production_requirements(self):
        """فتح نافذة مستلزمات الإنتاج"""
        window = tk.Toplevel(self.root)
        window.title("مستلزمات الإنتاج")
        window.geometry("900x700")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="مستلزمات الإنتاج للمشروع",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # قسم الأجهزة والمعدات
        equipment_label = tk.Label(
            scrollable_frame,
            text="1. الأجهزة والآلات والمعدات والأثاث المطلوبة عند التأسيس",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#2fa572"
        )
        equipment_label.pack(pady=10)

        # جدول الأجهزة
        equipment_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        equipment_frame.pack(pady=10)

        # عناوين الجدول
        headers = ["البند", "سعر الوحدة", "عدد الوحدات", "المجموع"]
        for i, header in enumerate(headers):
            header_label = tk.Label(
                equipment_frame,
                text=header,
                font=("Arial", 12, "bold"),
                bg="#3b82f6",
                fg="white",
                width=15
            )
            header_label.grid(row=0, column=3-i, padx=2, pady=2)  # RTL order

        # صفوف الأجهزة
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف
            row_entries = {}

            # البند
            item_entry = tk.Entry(equipment_frame, font=("Arial", 11), width=20, justify="right")
            item_entry.grid(row=row, column=3, padx=2, pady=2)
            row_entries["item"] = item_entry

            # سعر الوحدة
            price_entry = tk.Entry(equipment_frame, font=("Arial", 11), width=15, justify="center")
            price_entry.grid(row=row, column=2, padx=2, pady=2)
            price_entry.bind('<KeyRelease>', lambda e, r=row: self.calculate_equipment_total(r))
            row_entries["price"] = price_entry

            # عدد الوحدات
            quantity_entry = tk.Entry(equipment_frame, font=("Arial", 11), width=15, justify="center")
            quantity_entry.grid(row=row, column=1, padx=2, pady=2)
            quantity_entry.bind('<KeyRelease>', lambda e, r=row: self.calculate_equipment_total(r))
            row_entries["quantity"] = quantity_entry

            # المجموع
            total_label = tk.Label(equipment_frame, font=("Arial", 11), width=15, bg="#f0f0f0", relief="sunken")
            total_label.grid(row=row, column=0, padx=2, pady=2)
            row_entries["total"] = total_label

            self.equipment_entries.append(row_entries)

        # إجمالي الأجهزة
        self.equipment_total_label = tk.Label(
            scrollable_frame,
            text="إجمالي الأجهزة والمعدات: 0 دينار",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#2fa572"
        )
        self.equipment_total_label.pack(pady=10)

        # قسم المواد الخام
        materials_label = tk.Label(
            scrollable_frame,
            text="2. المواد الخام المطلوبة لمدة شهر",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#ff6b35"
        )
        materials_label.pack(pady=10)

        # جدول المواد الخام
        materials_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        materials_frame.pack(pady=10)

        # عناوين الجدول
        for i, header in enumerate(headers):
            header_label = tk.Label(
                materials_frame,
                text=header,
                font=("Arial", 12, "bold"),
                bg="#ff6b35",
                fg="white",
                width=15
            )
            header_label.grid(row=0, column=3-i, padx=2, pady=2)  # RTL order

        # صفوف المواد الخام
        self.materials_entries = []
        for row in range(1, 6):  # 5 صفوف
            row_entries = {}

            # البند
            item_entry = tk.Entry(materials_frame, font=("Arial", 11), width=20, justify="right")
            item_entry.grid(row=row, column=3, padx=2, pady=2)
            row_entries["item"] = item_entry

            # سعر الوحدة
            price_entry = tk.Entry(materials_frame, font=("Arial", 11), width=15, justify="center")
            price_entry.grid(row=row, column=2, padx=2, pady=2)
            price_entry.bind('<KeyRelease>', lambda e, r=row: self.calculate_materials_total(r))
            row_entries["price"] = price_entry

            # عدد الوحدات
            quantity_entry = tk.Entry(materials_frame, font=("Arial", 11), width=15, justify="center")
            quantity_entry.grid(row=row, column=1, padx=2, pady=2)
            quantity_entry.bind('<KeyRelease>', lambda e, r=row: self.calculate_materials_total(r))
            row_entries["quantity"] = quantity_entry

            # المجموع
            total_label = tk.Label(materials_frame, font=("Arial", 11), width=15, bg="#f0f0f0", relief="sunken")
            total_label.grid(row=row, column=0, padx=2, pady=2)
            row_entries["total"] = total_label

            self.materials_entries.append(row_entries)

        # إجمالي المواد الخام
        self.materials_total_label = tk.Label(
            scrollable_frame,
            text="إجمالي المواد الخام: 0 دينار",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#ff6b35"
        )
        self.materials_total_label.pack(pady=10)

        # تحميل البيانات المحفوظة
        self.load_production_data()

        # زر الحفظ
        save_btn = tk.Button(
            scrollable_frame,
            text="حفظ مستلزمات الإنتاج",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_production_requirements(window)
        )
        save_btn.pack(pady=20)

    def calculate_equipment_total(self, row_index):
        """حساب إجمالي صف الأجهزة"""
        try:
            row_entries = self.equipment_entries[row_index - 1]
            price = float(row_entries["price"].get() or 0)
            quantity = float(row_entries["quantity"].get() or 0)
            total = price * quantity
            row_entries["total"].config(text=f"{total:,.0f}")

            # حساب الإجمالي الكلي
            grand_total = 0
            for entries in self.equipment_entries:
                try:
                    total_text = entries["total"].cget("text")
                    if total_text and total_text != "0":
                        grand_total += float(total_text.replace(",", ""))
                except:
                    continue

            self.equipment_total_label.config(text=f"إجمالي الأجهزة والمعدات: {grand_total:,.0f} دينار")

        except ValueError:
            pass

    def calculate_materials_total(self, row_index):
        """حساب إجمالي صف المواد الخام"""
        try:
            row_entries = self.materials_entries[row_index - 1]
            price = float(row_entries["price"].get() or 0)
            quantity = float(row_entries["quantity"].get() or 0)
            total = price * quantity
            row_entries["total"].config(text=f"{total:,.0f}")

            # حساب الإجمالي الكلي
            grand_total = 0
            for entries in self.materials_entries:
                try:
                    total_text = entries["total"].cget("text")
                    if total_text and total_text != "0":
                        grand_total += float(total_text.replace(",", ""))
                except:
                    continue

            self.materials_total_label.config(text=f"إجمالي المواد الخام: {grand_total:,.0f} دينار")

        except ValueError:
            pass

    def load_production_data(self):
        """تحميل بيانات مستلزمات الإنتاج"""
        if "production_requirements" not in self.data:
            return

        # تحميل بيانات الأجهزة
        equipment_data = self.data["production_requirements"].get("equipment", [])
        for i, item_data in enumerate(equipment_data):
            if i < len(self.equipment_entries):
                entries = self.equipment_entries[i]
                entries["item"].insert(0, item_data.get("item", ""))
                entries["price"].insert(0, str(item_data.get("price", "")))
                entries["quantity"].insert(0, str(item_data.get("quantity", "")))
                self.calculate_equipment_total(i + 1)

        # تحميل بيانات المواد الخام
        materials_data = self.data["production_requirements"].get("materials", [])
        for i, item_data in enumerate(materials_data):
            if i < len(self.materials_entries):
                entries = self.materials_entries[i]
                entries["item"].insert(0, item_data.get("item", ""))
                entries["price"].insert(0, str(item_data.get("price", "")))
                entries["quantity"].insert(0, str(item_data.get("quantity", "")))
                self.calculate_materials_total(i + 1)

    def save_production_requirements(self, window):
        """حفظ مستلزمات الإنتاج"""
        if "production_requirements" not in self.data:
            self.data["production_requirements"] = {}

        # حفظ بيانات الأجهزة
        equipment_data = []
        for entries in self.equipment_entries:
            item = entries["item"].get().strip()
            if item:
                try:
                    equipment_data.append({
                        "item": item,
                        "price": float(entries["price"].get() or 0),
                        "quantity": float(entries["quantity"].get() or 0),
                        "total": float(entries["price"].get() or 0) * float(entries["quantity"].get() or 0)
                    })
                except ValueError:
                    continue

        # حفظ بيانات المواد الخام
        materials_data = []
        for entries in self.materials_entries:
            item = entries["item"].get().strip()
            if item:
                try:
                    materials_data.append({
                        "item": item,
                        "price": float(entries["price"].get() or 0),
                        "quantity": float(entries["quantity"].get() or 0),
                        "total": float(entries["price"].get() or 0) * float(entries["quantity"].get() or 0)
                    })
                except ValueError:
                    continue

        self.data["production_requirements"]["equipment"] = equipment_data
        self.data["production_requirements"]["materials"] = materials_data

        messagebox.showinfo("نجح الحفظ", "تم حفظ مستلزمات الإنتاج بنجاح!")
        window.destroy()
    
    def open_financial_study(self):
        """فتح نافذة الدراسة المالية"""
        window = tk.Toplevel(self.root)
        window.title("الدراسة المالية")
        window.geometry("800x700")
        window.configure(bg="#ffffff")
        
        # عنوان
        title = tk.Label(
            window,
            text="الدراسة المالية للمشروع",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # التكاليف الثابتة
        fixed_costs_label = tk.Label(
            scrollable_frame,
            text="التكاليف الثابتة الشهرية",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#2fa572"
        )
        fixed_costs_label.pack(pady=10)
        
        fixed_costs_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        fixed_costs_frame.pack(pady=10)
        
        fixed_cost_fields = [
            ("الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)", "fixed_salaries"),
            ("الإيجار", "rent"),
            ("الصيانة", "maintenance"),
            ("التسويق والدعاية", "marketing"),
            ("تكاليف ثابتة أخرى", "other_fixed")
        ]
        
        fixed_entries = {}
        
        for i, (label_text, field_key) in enumerate(fixed_cost_fields):
            label = tk.Label(
                fixed_costs_frame,
                text=label_text,
                font=("Arial", 12),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i, column=0, sticky="e", padx=10, pady=5)
            
            entry = tk.Entry(
                fixed_costs_frame,
                font=("Arial", 12),
                width=20,
                justify="center"
            )
            entry.grid(row=i, column=1, padx=10, pady=5)
            fixed_entries[field_key] = entry
            
            # تحميل البيانات
            if "fixed_costs" in self.data["financial_data"] and field_key in self.data["financial_data"]["fixed_costs"]:
                entry.insert(0, str(self.data["financial_data"]["fixed_costs"][field_key]))
            
            # وحدة العملة
            currency_label = tk.Label(
                fixed_costs_frame,
                text="دينار",
                font=("Arial", 12),
                bg="#ffffff"
            )
            currency_label.grid(row=i, column=2, padx=5, pady=5)
        
        # التكاليف المتغيرة
        variable_costs_label = tk.Label(
            scrollable_frame,
            text="التكاليف المتغيرة الشهرية",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#ff6b35"
        )
        variable_costs_label.pack(pady=10)
        
        variable_costs_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        variable_costs_frame.pack(pady=10)
        
        variable_cost_fields = [
            ("المواد الخام", "raw_materials"),
            ("أجور العمال المباشرين", "direct_labor"),
            ("فواتير الماء والكهرباء والهاتف والإنترنت", "utilities"),
            ("أجور النقل والمواصلات", "transportation"),
            ("تكاليف متغيرة أخرى", "other_variable")
        ]
        
        variable_entries = {}
        
        for i, (label_text, field_key) in enumerate(variable_cost_fields):
            label = tk.Label(
                variable_costs_frame,
                text=label_text,
                font=("Arial", 12),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i, column=0, sticky="e", padx=10, pady=5)
            
            entry = tk.Entry(
                variable_costs_frame,
                font=("Arial", 12),
                width=20,
                justify="center"
            )
            entry.grid(row=i, column=1, padx=10, pady=5)
            variable_entries[field_key] = entry
            
            # تحميل البيانات
            if "variable_costs" in self.data["financial_data"] and field_key in self.data["financial_data"]["variable_costs"]:
                entry.insert(0, str(self.data["financial_data"]["variable_costs"][field_key]))
            
            # وحدة العملة
            currency_label = tk.Label(
                variable_costs_frame,
                text="دينار",
                font=("Arial", 12),
                bg="#ffffff"
            )
            currency_label.grid(row=i, column=2, padx=5, pady=5)
        
        # الإيرادات المتوقعة
        revenue_label = tk.Label(
            scrollable_frame,
            text="الإيرادات المتوقعة الشهرية",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#3b82f6"
        )
        revenue_label.pack(pady=10)
        
        revenue_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        revenue_frame.pack(pady=10)
        
        revenue_entry_label = tk.Label(
            revenue_frame,
            text="إجمالي الإيرادات الشهرية المتوقعة",
            font=("Arial", 12),
            bg="#ffffff"
        )
        revenue_entry_label.grid(row=0, column=0, padx=10, pady=5)
        
        revenue_entry = tk.Entry(
            revenue_frame,
            font=("Arial", 12),
            width=20,
            justify="center"
        )
        revenue_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # تحميل البيانات
        if "monthly_revenue" in self.data["financial_data"]:
            revenue_entry.insert(0, str(self.data["financial_data"]["monthly_revenue"]))
        
        revenue_currency = tk.Label(
            revenue_frame,
            text="دينار",
            font=("Arial", 12),
            bg="#ffffff"
        )
        revenue_currency.grid(row=0, column=2, padx=5, pady=5)

        # رأس المال الثابت
        capital_label = tk.Label(
            scrollable_frame,
            text="رأس المال الثابت (تكاليف المشروع الرأسمالية)",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#8b5cf6"
        )
        capital_label.pack(pady=10)

        capital_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        capital_frame.pack(pady=10)

        capital_entry_label = tk.Label(
            capital_frame,
            text="رأس المال الثابت (المعدات، الأجهزة...)",
            font=("Arial", 12),
            bg="#ffffff"
        )
        capital_entry_label.grid(row=0, column=0, padx=10, pady=5)

        capital_entry = tk.Entry(
            capital_frame,
            font=("Arial", 12),
            width=20,
            justify="center"
        )
        capital_entry.grid(row=0, column=1, padx=10, pady=5)

        # تحميل البيانات
        if "fixed_capital" in self.data["financial_data"]:
            capital_entry.insert(0, str(self.data["financial_data"]["fixed_capital"]))

        capital_currency = tk.Label(
            capital_frame,
            text="دينار",
            font=("Arial", 12),
            bg="#ffffff"
        )
        capital_currency.grid(row=0, column=2, padx=5, pady=5)

        # النفقات التأسيسية
        startup_label = tk.Label(
            scrollable_frame,
            text="النفقات التأسيسية - ما قبل التشغيل",
            font=("Arial", 14, "bold"),
            bg="#ffffff",
            fg="#059669"
        )
        startup_label.pack(pady=10)

        startup_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        startup_frame.pack(pady=10)

        startup_fields = [
            ("التسجيل والترخيص", "registration_licensing"),
            ("توصيل الخدمات", "utilities_connection"),
            ("خلو / تجهيز الموقع", "location_setup"),
            ("تجهيز أولي", "initial_setup")
        ]

        startup_entries = {}

        for i, (label_text, field_key) in enumerate(startup_fields):
            label = tk.Label(
                startup_frame,
                text=label_text,
                font=("Arial", 12),
                bg="#ffffff",
                anchor="e"
            )
            label.grid(row=i, column=0, sticky="e", padx=10, pady=5)

            entry = tk.Entry(
                startup_frame,
                font=("Arial", 12),
                width=20,
                justify="center"
            )
            entry.grid(row=i, column=1, padx=10, pady=5)
            startup_entries[field_key] = entry

            # تحميل البيانات
            if "startup_costs" in self.data["financial_data"] and field_key in self.data["financial_data"]["startup_costs"]:
                entry.insert(0, str(self.data["financial_data"]["startup_costs"][field_key]))

            # وحدة العملة
            currency_label = tk.Label(
                startup_frame,
                text="دينار",
                font=("Arial", 12),
                bg="#ffffff"
            )
            currency_label.grid(row=i, column=2, padx=5, pady=5)
        
        # زر الحساب
        calculate_btn = tk.Button(
            scrollable_frame,
            text="احسب النتائج المالية",
            font=("Arial", 12),
            bg="#8b5cf6",
            fg="white",
            command=lambda: self.calculate_financial_results(fixed_entries, variable_entries, revenue_entry, capital_entry, startup_entries, scrollable_frame)
        )
        calculate_btn.pack(pady=20)
        
        # زر الحفظ
        save_btn = tk.Button(
            scrollable_frame,
            text="حفظ الدراسة المالية",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.save_financial_data(fixed_entries, variable_entries, revenue_entry, capital_entry, startup_entries, window)
        )
        save_btn.pack(pady=10)
    
    def calculate_financial_results(self, fixed_entries, variable_entries, revenue_entry, capital_entry, startup_entries, parent):
        """حساب النتائج المالية"""
        try:
            # حساب التكاليف الثابتة
            total_fixed = sum(float(entry.get() or 0) for entry in fixed_entries.values())

            # حساب التكاليف المتغيرة
            total_variable = sum(float(entry.get() or 0) for entry in variable_entries.values())

            # الإيرادات
            monthly_revenue = float(revenue_entry.get() or 0)

            # رأس المال الثابت
            fixed_capital = float(capital_entry.get() or 0)

            # النفقات التأسيسية
            total_startup = sum(float(entry.get() or 0) for entry in startup_entries.values())

            # حساب الربح
            monthly_profit = monthly_revenue - total_fixed - total_variable
            annual_profit = monthly_profit * 12

            # إجمالي رأس المال المطلوب
            working_capital = total_fixed + total_variable  # رأس المال العامل لشهر واحد
            total_capital_needed = fixed_capital + working_capital + total_startup
            
            # عرض النتائج
            results_frame = tk.Frame(parent, bg="#ffffff", relief="ridge", bd=2)
            results_frame.pack(pady=20, padx=20, fill="x")
            
            results_title = tk.Label(
                results_frame,
                text="النتائج المالية",
                font=("Arial", 14, "bold"),
                bg="#ffffff",
                fg="#059669"
            )
            results_title.pack(pady=10)
            
            results_text = f"""
═══════════════════════════════════════════════════════════
                    النتائج المالية للمشروع
═══════════════════════════════════════════════════════════

💰 التكاليف الشهرية:
   • التكاليف الثابتة: {total_fixed:,.0f} دينار
   • التكاليف المتغيرة: {total_variable:,.0f} دينار
   • إجمالي التكاليف الشهرية: {total_fixed + total_variable:,.0f} دينار

💵 الإيرادات والأرباح:
   • الإيرادات الشهرية المتوقعة: {monthly_revenue:,.0f} دينار
   • صافي الربح الشهري: {monthly_profit:,.0f} دينار
   • صافي الربح السنوي: {annual_profit:,.0f} دينار

💼 رأس المال المطلوب:
   • رأس المال الثابت: {fixed_capital:,.0f} دينار
   • رأس المال العامل (شهر واحد): {working_capital:,.0f} دينار
   • النفقات التأسيسية: {total_startup:,.0f} دينار
   • إجمالي رأس المال المطلوب: {total_capital_needed:,.0f} دينار

🎯 تقييم المشروع: {"✅ مربح" if monthly_profit > 0 else "❌ خاسر" if monthly_profit < 0 else "⚖️ متعادل"}

📊 مؤشرات الأداء:
   • نسبة الربح الشهري: {(monthly_profit/monthly_revenue*100) if monthly_revenue > 0 else 0:.1f}%
   • فترة استرداد رأس المال: {(total_capital_needed/monthly_profit) if monthly_profit > 0 else "غير محدد"} شهر
            """
            
            results_label = tk.Label(
                results_frame,
                text=results_text,
                font=("Arial", 11),
                bg="#ffffff",
                justify="right"
            )
            results_label.pack(pady=10)
            
            # حفظ النتائج
            self.data["financial_data"]["results"] = {
                "total_fixed": total_fixed,
                "total_variable": total_variable,
                "monthly_revenue": monthly_revenue,
                "monthly_profit": monthly_profit,
                "annual_profit": annual_profit
            }
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة في جميع الحقول")
    
    def save_financial_data(self, fixed_entries, variable_entries, revenue_entry, capital_entry, startup_entries, window):
        """حفظ البيانات المالية"""
        # حفظ التكاليف الثابتة
        self.data["financial_data"]["fixed_costs"] = {}
        for field_key, entry in fixed_entries.items():
            try:
                self.data["financial_data"]["fixed_costs"][field_key] = float(entry.get() or 0)
            except ValueError:
                self.data["financial_data"]["fixed_costs"][field_key] = 0

        # حفظ التكاليف المتغيرة
        self.data["financial_data"]["variable_costs"] = {}
        for field_key, entry in variable_entries.items():
            try:
                self.data["financial_data"]["variable_costs"][field_key] = float(entry.get() or 0)
            except ValueError:
                self.data["financial_data"]["variable_costs"][field_key] = 0

        # حفظ الإيرادات
        try:
            self.data["financial_data"]["monthly_revenue"] = float(revenue_entry.get() or 0)
        except ValueError:
            self.data["financial_data"]["monthly_revenue"] = 0

        # حفظ رأس المال الثابت
        try:
            self.data["financial_data"]["fixed_capital"] = float(capital_entry.get() or 0)
        except ValueError:
            self.data["financial_data"]["fixed_capital"] = 0

        # حفظ النفقات التأسيسية
        self.data["financial_data"]["startup_costs"] = {}
        for field_key, entry in startup_entries.items():
            try:
                self.data["financial_data"]["startup_costs"][field_key] = float(entry.get() or 0)
            except ValueError:
                self.data["financial_data"]["startup_costs"][field_key] = 0

        messagebox.showinfo("نجح الحفظ", "تم حفظ الدراسة المالية بنجاح!")
        window.destroy()

    def open_breakeven_calculator(self):
        """فتح حاسبة نقطة التعادل"""
        window = tk.Toplevel(self.root)
        window.title("نقطة التعادل وحاسبة الاحتساب")
        window.geometry("700x600")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="حساب نقطة التعادل",
            font=("Arial", 16, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # شرح نقطة التعادل
        explanation = tk.Label(
            window,
            text="نقطة التعادل هي عدد الوحدات التي يجب بيعها لتغطية جميع التكاليف",
            font=("Arial", 12),
            bg="#ffffff",
            fg="#6b7280"
        )
        explanation.pack(pady=10)

        # إطار المدخلات
        inputs_frame = tk.Frame(window, bg="#ffffff")
        inputs_frame.pack(pady=20)

        # التكاليف الثابتة الشهرية
        fixed_costs_label = tk.Label(
            inputs_frame,
            text="التكاليف الثابتة الشهرية",
            font=("Arial", 12),
            bg="#ffffff"
        )
        fixed_costs_label.grid(row=0, column=0, padx=10, pady=10, sticky="e")

        fixed_costs_entry = tk.Entry(
            inputs_frame,
            font=("Arial", 12),
            width=20,
            justify="center"
        )
        fixed_costs_entry.grid(row=0, column=1, padx=10, pady=10)

        # جلب من الدراسة المالية
        if "financial_data" in self.data and "results" in self.data["financial_data"]:
            fixed_costs_entry.insert(0, str(self.data["financial_data"]["results"]["total_fixed"]))

        tk.Label(inputs_frame, text="دينار", font=("Arial", 12), bg="#ffffff").grid(row=0, column=2, padx=5, pady=10)

        # متوسط تكلفة الوحدة المتغيرة
        variable_cost_label = tk.Label(
            inputs_frame,
            text="متوسط تكلفة الوحدة المتغيرة",
            font=("Arial", 12),
            bg="#ffffff"
        )
        variable_cost_label.grid(row=1, column=0, padx=10, pady=10, sticky="e")

        variable_cost_entry = tk.Entry(
            inputs_frame,
            font=("Arial", 12),
            width=20,
            justify="center"
        )
        variable_cost_entry.grid(row=1, column=1, padx=10, pady=10)

        # تحميل البيانات المحفوظة
        if "variable_cost_per_unit" in self.data["breakeven_data"]:
            variable_cost_entry.insert(0, str(self.data["breakeven_data"]["variable_cost_per_unit"]))

        tk.Label(inputs_frame, text="دينار", font=("Arial", 12), bg="#ffffff").grid(row=1, column=2, padx=5, pady=10)

        # متوسط سعر بيع الوحدة
        selling_price_label = tk.Label(
            inputs_frame,
            text="متوسط سعر بيع الوحدة",
            font=("Arial", 12),
            bg="#ffffff"
        )
        selling_price_label.grid(row=2, column=0, padx=10, pady=10, sticky="e")

        selling_price_entry = tk.Entry(
            inputs_frame,
            font=("Arial", 12),
            width=20,
            justify="center"
        )
        selling_price_entry.grid(row=2, column=1, padx=10, pady=10)

        # تحميل البيانات المحفوظة
        if "selling_price_per_unit" in self.data["breakeven_data"]:
            selling_price_entry.insert(0, str(self.data["breakeven_data"]["selling_price_per_unit"]))

        tk.Label(inputs_frame, text="دينار", font=("Arial", 12), bg="#ffffff").grid(row=2, column=2, padx=5, pady=10)

        # زر الحساب
        calculate_btn = tk.Button(
            window,
            text="احسب نقطة التعادل",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.calculate_breakeven(
                fixed_costs_entry, variable_cost_entry, selling_price_entry, window
            )
        )
        calculate_btn.pack(pady=20)

        # زر الحفظ
        save_btn = tk.Button(
            window,
            text="حفظ بيانات نقطة التعادل",
            font=("Arial", 12),
            bg="#3b82f6",
            fg="white",
            command=lambda: self.save_breakeven_data(
                fixed_costs_entry, variable_cost_entry, selling_price_entry, window
            )
        )
        save_btn.pack(pady=10)

    def calculate_breakeven(self, fixed_costs_entry, variable_cost_entry, selling_price_entry, parent):
        """حساب نقطة التعادل"""
        try:
            fixed_costs = float(fixed_costs_entry.get() or 0)
            variable_cost = float(variable_cost_entry.get() or 0)
            selling_price = float(selling_price_entry.get() or 0)

            if selling_price <= variable_cost:
                messagebox.showerror("خطأ", "سعر البيع يجب أن يكون أكبر من تكلفة الوحدة المتغيرة")
                return

            if fixed_costs <= 0:
                messagebox.showerror("خطأ", "التكاليف الثابتة يجب أن تكون أكبر من صفر")
                return

            # حساب نقطة التعادل
            contribution_margin = selling_price - variable_cost
            breakeven_units = fixed_costs / contribution_margin
            breakeven_revenue = breakeven_units * selling_price
            contribution_margin_ratio = contribution_margin / selling_price * 100

            # عرض النتائج
            results_window = tk.Toplevel(parent)
            results_window.title("نتائج نقطة التعادل")
            results_window.geometry("500x400")
            results_window.configure(bg="#ffffff")

            results_title = tk.Label(
                results_window,
                text="نتائج حساب نقطة التعادل",
                font=("Arial", 16, "bold"),
                bg="#ffffff",
                fg="#059669"
            )
            results_title.pack(pady=20)

            results_text = f"""
نقطة التعادل (عدد الوحدات): {breakeven_units:,.0f} وحدة شهرياً

نقطة التعادل (الإيرادات): {breakeven_revenue:,.0f} دينار شهرياً

هامش المساهمة للوحدة: {contribution_margin:,.0f} دينار

نسبة هامش المساهمة: {contribution_margin_ratio:.1f}%

═══════════════════════════════════════════════════════════

التفسير:
• يجب بيع {breakeven_units:,.0f} وحدة شهرياً على الأقل لتغطية التكاليف
• الإيرادات المطلوبة للتعادل: {breakeven_revenue:,.0f} دينار شهرياً
• كل وحدة تساهم بـ {contribution_margin:,.0f} دينار في تغطية التكاليف الثابتة

التوصية:
{"المشروع مجدي إذا كان بإمكانك بيع أكثر من " + f"{breakeven_units:,.0f}" + " وحدة شهرياً"}
            """

            results_label = tk.Label(
                results_window,
                text=results_text,
                font=("Arial", 11),
                bg="#ffffff",
                justify="right"
            )
            results_label.pack(pady=20, padx=20)

            # حفظ النتائج
            self.data["breakeven_data"]["results"] = {
                "breakeven_units": breakeven_units,
                "breakeven_revenue": breakeven_revenue,
                "contribution_margin": contribution_margin,
                "contribution_margin_ratio": contribution_margin_ratio
            }

            close_btn = tk.Button(
                results_window,
                text="إغلاق",
                font=("Arial", 12),
                bg="#6b7280",
                fg="white",
                command=results_window.destroy
            )
            close_btn.pack(pady=20)

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة في جميع الحقول")

    def save_breakeven_data(self, fixed_costs_entry, variable_cost_entry, selling_price_entry, window):
        """حفظ بيانات نقطة التعادل"""
        try:
            self.data["breakeven_data"]["fixed_costs"] = float(fixed_costs_entry.get() or 0)
            self.data["breakeven_data"]["variable_cost_per_unit"] = float(variable_cost_entry.get() or 0)
            self.data["breakeven_data"]["selling_price_per_unit"] = float(selling_price_entry.get() or 0)

            messagebox.showinfo("نجح الحفظ", "تم حفظ بيانات نقطة التعادل بنجاح!")
            window.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة في جميع الحقول")

    def open_summary_report(self):
        """فتح الملخص والتقرير النهائي"""
        window = tk.Toplevel(self.root)
        window.title("الملخص والتقرير النهائي")
        window.geometry("900x700")
        window.configure(bg="#ffffff")

        # عنوان
        title = tk.Label(
            window,
            text="الملخص النهائي لدراسة الجدوى",
            font=("Arial", 18, "bold"),
            bg="#ffffff",
            fg="#1f538d"
        )
        title.pack(pady=20)

        # إطار قابل للتمرير
        canvas = tk.Canvas(window, bg="#ffffff")
        scrollbar = ttk.Scrollbar(window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # توليد الملخص
        summary_text = self.generate_summary()

        summary_label = tk.Label(
            scrollable_frame,
            text=summary_text,
            font=("Arial", 11),
            bg="#ffffff",
            justify="right",
            wraplength=800
        )
        summary_label.pack(pady=20, padx=20)

        # أزرار التصدير
        export_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        export_frame.pack(pady=20)

        export_txt_btn = tk.Button(
            export_frame,
            text="تصدير تقرير نصي",
            font=("Arial", 12),
            bg="#2fa572",
            fg="white",
            command=lambda: self.export_text_report(summary_text)
        )
        export_txt_btn.pack(side="left", padx=10)

        export_json_btn = tk.Button(
            export_frame,
            text="تصدير البيانات JSON",
            font=("Arial", 12),
            bg="#3b82f6",
            fg="white",
            command=self.export_json_data
        )
        export_json_btn.pack(side="left", padx=10)

        print_btn = tk.Button(
            export_frame,
            text="طباعة التقرير",
            font=("Arial", 12),
            bg="#8b5cf6",
            fg="white",
            command=lambda: messagebox.showinfo("طباعة", "تم إرسال التقرير للطابعة")
        )
        print_btn.pack(side="left", padx=10)

    def generate_summary(self):
        """توليد الملخص النهائي"""
        try:
            personal = self.data.get("personal_info", {})
            project = self.data.get("project_info", {})
            financial = self.data.get("financial_data", {})
            breakeven = self.data.get("breakeven_data", {})
            market = self.data.get("market_analysis", {})
            swot = self.data.get("swot_analysis", {})
            marketing_mix = self.data.get("marketing_mix", {})
            production = self.data.get("production_requirements", {})

            # حساب النتائج
            monthly_profit = 0
            if "results" in financial:
                monthly_profit = financial["results"].get("monthly_profit", 0)

            breakeven_units = 0
            if "results" in breakeven:
                breakeven_units = breakeven["results"].get("breakeven_units", 0)

            # تقييم الجدوى
            feasibility_status = "مجدي مالياً" if monthly_profit > 0 else "غير مجدي مالياً" if monthly_profit < 0 else "متعادل"

            summary = f"""
═══════════════════════════════════════════════════════════════════
                        تقرير دراسة الجدوى الشامل
═══════════════════════════════════════════════════════════════════

تاريخ إعداد التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

═══════════════════════════════════════════════════════════════════
                            المعلومات الأساسية
═══════════════════════════════════════════════════════════════════

🏢 اسم المشروع: {project.get('project_name', 'غير محدد')}
👤 صاحب المشروع: {personal.get('owner_name', 'غير محدد')}
📍 موقع المشروع: {project.get('project_location', 'غير محدد')}
📞 رقم الهاتف: {personal.get('phone', 'غير محدد')}

💰 قيمة المنحة المطلوبة: {project.get('grant_amount', 'غير محدد')} دينار
💵 التمويل الذاتي: {project.get('self_funding', 'غير محدد')} دينار
💼 تكلفة المشروع الكلية: {project.get('total_cost', 'غير محدد')} دينار

📝 وصف المشروع:
{project.get('project_summary', 'غير محدد')[:200]}...

🎯 أهمية المشروع:
{project.get('importance_description', 'غير محدد')[:200]}...

═══════════════════════════════════════════════════════════════════
                        دراسة السوق والمنافسين
═══════════════════════════════════════════════════════════════════

🛍️ المنتجات المقدمة: {market.get('products_offered', 'غير محدد')[:100]}...
🏪 الخدمات المقدمة: {market.get('services_offered', 'غير محدد')[:100]}...
🏢 عدد المنافسين: {market.get('competitors_count', 'غير محدد')}
👥 الزبائن المحتملين: {market.get('potential_customers', 'غير محدد')}
📈 الاستراتيجية التسويقية: {market.get('marketing_strategy', 'غير محدد')[:100]}...

═══════════════════════════════════════════════════════════════════
                        التحليل الرباعي SWOT
═══════════════════════════════════════════════════════════════════

💪 نقاط القوة: {len(swot.get('strengths', '').split('\\n')) if swot.get('strengths') else 0} نقطة
⚠️ نقاط الضعف: {len(swot.get('weaknesses', '').split('\\n')) if swot.get('weaknesses') else 0} نقطة
🌟 الفرص: {len(swot.get('opportunities', '').split('\\n')) if swot.get('opportunities') else 0} فرصة
⚡ التهديدات: {len(swot.get('threats', '').split('\\n')) if swot.get('threats') else 0} تهديد

═══════════════════════════════════════════════════════════════════
                        المزيج التسويقي
═══════════════════════════════════════════════════════════════════

🛍️ المنتج: {marketing_mix.get('product_products', 'غير محدد')[:50]}...
💰 السعر: {marketing_mix.get('price_selling_prices', 'غير محدد')[:50]}...
📍 المكان: {marketing_mix.get('place_distribution', 'غير محدد')[:50]}...
📢 الترويج: {marketing_mix.get('promotion_strategy', 'غير محدد')[:50]}...
👥 الناس: {marketing_mix.get('people_helpers', 'غير محدد')[:50]}...

═══════════════════════════════════════════════════════════════════
                        مستلزمات الإنتاج
═══════════════════════════════════════════════════════════════════

🔧 عدد الأجهزة والمعدات: {len(production.get('equipment', []))} بند
🧪 عدد المواد الخام: {len(production.get('materials', []))} بند
💰 إجمالي تكلفة الأجهزة: {sum(item.get('total', 0) for item in production.get('equipment', [])):,.0f} دينار
💵 إجمالي تكلفة المواد الخام: {sum(item.get('total', 0) for item in production.get('materials', [])):,.0f} دينار

═══════════════════════════════════════════════════════════════════
                            الملخص المالي
═══════════════════════════════════════════════════════════════════
"""

            if "results" in financial:
                results = financial["results"]
                summary += f"""
📊 التكاليف الشهرية:
   • التكاليف الثابتة: {results.get('total_fixed', 0):,.0f} دينار
   • التكاليف المتغيرة: {results.get('total_variable', 0):,.0f} دينار
   • إجمالي التكاليف: {results.get('total_fixed', 0) + results.get('total_variable', 0):,.0f} دينار

💵 الإيرادات والأرباح:
   • الإيرادات الشهرية المتوقعة: {results.get('monthly_revenue', 0):,.0f} دينار
   • صافي الربح الشهري: {results.get('monthly_profit', 0):,.0f} دينار
   • صافي الربح السنوي: {results.get('annual_profit', 0):,.0f} دينار
"""

            if "results" in breakeven:
                breakeven_results = breakeven["results"]
                summary += f"""
═══════════════════════════════════════════════════════════════════
                            نقطة التعادل
═══════════════════════════════════════════════════════════════════

📍 نقطة التعادل: {breakeven_results.get('breakeven_units', 0):,.0f} وحدة شهرياً
💰 الإيرادات المطلوبة للتعادل: {breakeven_results.get('breakeven_revenue', 0):,.0f} دينار شهرياً
📈 هامش المساهمة: {breakeven_results.get('contribution_margin', 0):,.0f} دينار للوحدة
📊 نسبة هامش المساهمة: {breakeven_results.get('contribution_margin_ratio', 0):.1f}%
"""

            summary += f"""
═══════════════════════════════════════════════════════════════════
                            تقييم الجدوى
═══════════════════════════════════════════════════════════════════

🎯 الحالة المالية: {feasibility_status}

💡 التوصيات:
"""

            if monthly_profit > 0:
                summary += """
✅ المشروع مجدي مالياً ويُنصح بالتنفيذ
• المشروع يحقق أرباحاً شهرية وسنوية
• يُنصح بالبدء في التنفيذ مع مراقبة الأداء
• الاحتفاظ بسيولة نقدية كافية للطوارئ
"""
            elif monthly_profit == 0:
                summary += """
⚠️ المشروع متعادل مالياً
• المشروع يغطي تكاليفه فقط دون تحقيق أرباح
• يُنصح بمراجعة الأسعار أو تقليل التكاليف
• دراسة إمكانية زيادة المبيعات
"""
            else:
                summary += """
❌ المشروع غير مجدي مالياً
• المشروع يحقق خسائر شهرية
• يُنصح بإعادة دراسة المشروع
• مراجعة هيكل التكاليف والأسعار
• البحث عن طرق لزيادة الإيرادات أو تقليل التكاليف
"""

            summary += f"""

═══════════════════════════════════════════════════════════════════
                            خطوات التنفيذ المقترحة
═══════════════════════════════════════════════════════════════════

1. الحصول على التراخيص والموافقات المطلوبة
2. تأمين التمويل اللازم ({project.get('total_cost', 'غير محدد')} دينار)
3. إعداد الموقع وشراء المعدات
4. توظيف الكادر المطلوب وتدريبه
5. بدء التشغيل التجريبي
6. إطلاق حملة تسويقية
7. التشغيل الكامل ومراقبة الأداء

═══════════════════════════════════════════════════════════════════
                                خاتمة
═══════════════════════════════════════════════════════════════════

تم إعداد هذا التقرير باستخدام تطبيق دراسة الجدوى الشامل.
يُنصح بمراجعة هذه الدراسة مع خبراء في المجال قبل اتخاذ قرار الاستثمار.

تاريخ الإعداد: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

═══════════════════════════════════════════════════════════════════
"""

            return summary

        except Exception as e:
            return f"خطأ في توليد الملخص: {str(e)}"

    def export_text_report(self, summary_text):
        """تصدير التقرير كملف نصي"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt")],
                title="حفظ التقرير النصي"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(summary_text)

                messagebox.showinfo("نجح التصدير", f"تم حفظ التقرير في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير:\n{str(e)}")

    def export_json_data(self):
        """تصدير البيانات كملف JSON"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")],
                title="حفظ البيانات JSON"
            )

            if filename:
                # إضافة معلومات التصدير
                export_data = self.data.copy()
                export_data["export_info"] = {
                    "export_date": datetime.now().isoformat(),
                    "version": "1.0",
                    "exported_by": "تطبيق دراسة الجدوى الشامل - النسخة المبسطة"
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("نجح التصدير", f"تم حفظ البيانات في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"حدث خطأ أثناء تصدير البيانات:\n{str(e)}")

    def save_data(self):
        """حفظ جميع البيانات"""
        try:
            # إنشاء مجلد البيانات
            os.makedirs("data", exist_ok=True)

            # حفظ البيانات
            filename = f"data/feasibility_study_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح الحفظ", f"تم حفظ جميع البيانات في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")

    def load_data(self):
        """تحميل البيانات"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json")],
                title="تحميل البيانات"
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)

                # دمج البيانات المحملة
                for section, data in loaded_data.items():
                    if section in self.data:
                        self.data[section].update(data)

                messagebox.showinfo("نجح التحميل", "تم تحميل البيانات بنجاح!")

        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = FeasibilityStudyApp()
    app.run()
