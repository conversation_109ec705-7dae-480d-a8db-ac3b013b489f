#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة مستلزمات الإنتاج
Production Requirements Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox

class ProductionRequirementsFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="🛠️ مستلزمات الإنتاج")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="مستلزمات الإنتاج للمشروع",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # قسم الأجهزة والمعدات
        self.create_equipment_section()
        
        # قسم المواد الخام
        self.create_raw_materials_section()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_equipment_section(self):
        """إنشاء قسم الأجهزة والمعدات"""
        equipment_frame = ctk.CTkFrame(self.scrollable_frame)
        equipment_frame.pack(fill="x", padx=20, pady=10)
        
        equipment_title = ctk.CTkLabel(
            equipment_frame,
            text="🔧 1. الأجهزة والآلات والمعدات والأثاث المطلوبة عند التأسيس",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        equipment_title.pack(pady=(15, 10))
        
        # شرح
        equipment_desc = ctk.CTkLabel(
            equipment_frame,
            text="مثال: ماكينة خياطة – عجانة – مولينكس – طاولات – كراسي – أرفف ...",
            font=self.arabic_font,
            text_color=("#6b7280", "#9ca3af")
        )
        equipment_desc.pack(pady=(0, 10))
        
        # إطار الجدول
        equipment_table_frame = ctk.CTkFrame(equipment_frame)
        equipment_table_frame.pack(fill="x", padx=20, pady=10)
        
        # عناوين الجدول
        headers_frame = ctk.CTkFrame(equipment_table_frame, fg_color="transparent")
        headers_frame.pack(fill="x", padx=10, pady=5)
        
        # العناوين من اليمين لليسار (RTL)
        total_header = ctk.CTkLabel(
            headers_frame,
            text="💰 المجموع",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#3b82f6", "#3b82f6"),
            corner_radius=5
        )
        total_header.pack(side="left", padx=2)
        
        quantity_header = ctk.CTkLabel(
            headers_frame,
            text="🔢 عدد الوحدات",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#3b82f6", "#3b82f6"),
            corner_radius=5
        )
        quantity_header.pack(side="left", padx=2)
        
        price_header = ctk.CTkLabel(
            headers_frame,
            text="💵 سعر الوحدة",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#3b82f6", "#3b82f6"),
            corner_radius=5
        )
        price_header.pack(side="left", padx=2)
        
        item_header = ctk.CTkLabel(
            headers_frame,
            text="🧾 البند",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            corner_radius=5
        )
        item_header.pack(side="right", padx=2)
        
        # إطار الصفوف
        self.equipment_rows_frame = ctk.CTkFrame(equipment_table_frame)
        self.equipment_rows_frame.pack(fill="x", padx=10, pady=5)
        
        # قائمة الصفوف
        self.equipment_rows = []
        
        # إضافة صفوف افتراضية
        for i in range(5):
            self.add_equipment_row()
        
        # أزرار إدارة الجدول
        equipment_buttons_frame = ctk.CTkFrame(equipment_frame, fg_color="transparent")
        equipment_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        add_equipment_btn = ctk.CTkButton(
            equipment_buttons_frame,
            text="➕ إضافة صف",
            font=self.arabic_font,
            width=120,
            height=30,
            command=self.add_equipment_row
        )
        add_equipment_btn.pack(side="right", padx=5)
        
        remove_equipment_btn = ctk.CTkButton(
            equipment_buttons_frame,
            text="➖ حذف صف",
            font=self.arabic_font,
            width=120,
            height=30,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.remove_equipment_row
        )
        remove_equipment_btn.pack(side="right", padx=5)
        
        # إجمالي الأجهزة
        self.equipment_total_frame = ctk.CTkFrame(equipment_frame)
        self.equipment_total_frame.pack(fill="x", padx=20, pady=10)
        
        self.equipment_total_label = ctk.CTkLabel(
            self.equipment_total_frame,
            text="الإجمالي الكلي: 0 دينار عراقي",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        self.equipment_total_label.pack(pady=10)
    
    def create_raw_materials_section(self):
        """إنشاء قسم المواد الخام"""
        materials_frame = ctk.CTkFrame(self.scrollable_frame)
        materials_frame.pack(fill="x", padx=20, pady=10)
        
        materials_title = ctk.CTkLabel(
            materials_frame,
            text="🧪 2. المواد الخام المطلوبة لمدة شهر",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        materials_title.pack(pady=(15, 10))
        
        # شرح
        materials_desc = ctk.CTkLabel(
            materials_frame,
            text="مثال: طحين – قماش – صبغات – خيوط – أعلاف ...",
            font=self.arabic_font,
            text_color=("#6b7280", "#9ca3af")
        )
        materials_desc.pack(pady=(0, 10))
        
        # إطار الجدول
        materials_table_frame = ctk.CTkFrame(materials_frame)
        materials_table_frame.pack(fill="x", padx=20, pady=10)
        
        # عناوين الجدول
        materials_headers_frame = ctk.CTkFrame(materials_table_frame, fg_color="transparent")
        materials_headers_frame.pack(fill="x", padx=10, pady=5)
        
        # العناوين من اليمين لليسار (RTL)
        total_header2 = ctk.CTkLabel(
            materials_headers_frame,
            text="💰 المجموع",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#ff6b35", "#ff6b35"),
            corner_radius=5
        )
        total_header2.pack(side="left", padx=2)
        
        quantity_header2 = ctk.CTkLabel(
            materials_headers_frame,
            text="🔢 عدد الوحدات",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#ff6b35", "#ff6b35"),
            corner_radius=5
        )
        quantity_header2.pack(side="left", padx=2)
        
        price_header2 = ctk.CTkLabel(
            materials_headers_frame,
            text="💵 سعر الوحدة",
            font=self.arabic_font_bold,
            width=120,
            fg_color=("#ff6b35", "#ff6b35"),
            corner_radius=5
        )
        price_header2.pack(side="left", padx=2)
        
        item_header2 = ctk.CTkLabel(
            materials_headers_frame,
            text="🧾 البند",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#ff6b35", "#ff6b35"),
            corner_radius=5
        )
        item_header2.pack(side="right", padx=2)
        
        # إطار الصفوف
        self.materials_rows_frame = ctk.CTkFrame(materials_table_frame)
        self.materials_rows_frame.pack(fill="x", padx=10, pady=5)
        
        # قائمة الصفوف
        self.materials_rows = []
        
        # إضافة صفوف افتراضية
        for i in range(5):
            self.add_materials_row()
        
        # أزرار إدارة الجدول
        materials_buttons_frame = ctk.CTkFrame(materials_frame, fg_color="transparent")
        materials_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        add_materials_btn = ctk.CTkButton(
            materials_buttons_frame,
            text="➕ إضافة صف",
            font=self.arabic_font,
            width=120,
            height=30,
            command=self.add_materials_row
        )
        add_materials_btn.pack(side="right", padx=5)
        
        remove_materials_btn = ctk.CTkButton(
            materials_buttons_frame,
            text="➖ حذف صف",
            font=self.arabic_font,
            width=120,
            height=30,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.remove_materials_row
        )
        remove_materials_btn.pack(side="right", padx=5)
        
        # إجمالي المواد الخام
        self.materials_total_frame = ctk.CTkFrame(materials_frame)
        self.materials_total_frame.pack(fill="x", padx=20, pady=10)
        
        self.materials_total_label = ctk.CTkLabel(
            self.materials_total_frame,
            text="الإجمالي الكلي: 0 دينار عراقي",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        self.materials_total_label.pack(pady=10)
    
    def add_equipment_row(self):
        """إضافة صف جديد لجدول الأجهزة"""
        row_frame = ctk.CTkFrame(self.equipment_rows_frame, fg_color="transparent")
        row_frame.pack(fill="x", pady=2)
        
        # المجموع (محسوب تلقائياً)
        total_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center",
            state="readonly"
        )
        total_entry.pack(side="left", padx=2)
        
        # عدد الوحدات
        quantity_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center"
        )
        quantity_entry.pack(side="left", padx=2)
        quantity_entry.bind('<KeyRelease>', lambda e: self.calculate_equipment_totals())
        
        # سعر الوحدة
        price_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center"
        )
        price_entry.pack(side="left", padx=2)
        price_entry.bind('<KeyRelease>', lambda e: self.calculate_equipment_totals())
        
        # البند
        item_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=200,
            justify="right"
        )
        item_entry.pack(side="right", padx=2)
        
        # حفظ مراجع الحقول
        row_data = {
            'frame': row_frame,
            'item': item_entry,
            'price': price_entry,
            'quantity': quantity_entry,
            'total': total_entry
        }
        
        self.equipment_rows.append(row_data)
    
    def add_materials_row(self):
        """إضافة صف جديد لجدول المواد الخام"""
        row_frame = ctk.CTkFrame(self.materials_rows_frame, fg_color="transparent")
        row_frame.pack(fill="x", pady=2)
        
        # المجموع (محسوب تلقائياً)
        total_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center",
            state="readonly"
        )
        total_entry.pack(side="left", padx=2)
        
        # عدد الوحدات
        quantity_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center"
        )
        quantity_entry.pack(side="left", padx=2)
        quantity_entry.bind('<KeyRelease>', lambda e: self.calculate_materials_totals())
        
        # سعر الوحدة
        price_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=120,
            justify="center"
        )
        price_entry.pack(side="left", padx=2)
        price_entry.bind('<KeyRelease>', lambda e: self.calculate_materials_totals())
        
        # البند
        item_entry = ctk.CTkEntry(
            row_frame,
            font=self.arabic_font,
            width=200,
            justify="right"
        )
        item_entry.pack(side="right", padx=2)
        
        # حفظ مراجع الحقول
        row_data = {
            'frame': row_frame,
            'item': item_entry,
            'price': price_entry,
            'quantity': quantity_entry,
            'total': total_entry
        }
        
        self.materials_rows.append(row_data)
    
    def remove_equipment_row(self):
        """حذف آخر صف من جدول الأجهزة"""
        if len(self.equipment_rows) > 1:
            row_data = self.equipment_rows.pop()
            row_data['frame'].destroy()
            self.calculate_equipment_totals()
    
    def remove_materials_row(self):
        """حذف آخر صف من جدول المواد الخام"""
        if len(self.materials_rows) > 1:
            row_data = self.materials_rows.pop()
            row_data['frame'].destroy()
            self.calculate_materials_totals()
    
    def calculate_equipment_totals(self):
        """حساب إجماليات الأجهزة"""
        grand_total = 0
        
        for row_data in self.equipment_rows:
            try:
                price = float(row_data['price'].get() or 0)
                quantity = float(row_data['quantity'].get() or 0)
                total = price * quantity
                
                # تحديث المجموع للصف
                row_data['total'].configure(state="normal")
                row_data['total'].delete(0, tk.END)
                row_data['total'].insert(0, f"{total:,.0f}")
                row_data['total'].configure(state="readonly")
                
                grand_total += total
                
            except ValueError:
                continue
        
        # تحديث الإجمالي الكلي
        self.equipment_total_label.configure(
            text=f"الإجمالي الكلي: {grand_total:,.0f} دينار عراقي"
        )
    
    def calculate_materials_totals(self):
        """حساب إجماليات المواد الخام"""
        grand_total = 0
        
        for row_data in self.materials_rows:
            try:
                price = float(row_data['price'].get() or 0)
                quantity = float(row_data['quantity'].get() or 0)
                total = price * quantity
                
                # تحديث المجموع للصف
                row_data['total'].configure(state="normal")
                row_data['total'].delete(0, tk.END)
                row_data['total'].insert(0, f"{total:,.0f}")
                row_data['total'].configure(state="readonly")
                
                grand_total += total
                
            except ValueError:
                continue
        
        # تحديث الإجمالي الكلي
        self.materials_total_label.configure(
            text=f"الإجمالي الكلي: {grand_total:,.0f} دينار عراقي"
        )
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ مستلزمات الإنتاج",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الجداول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: الدراسة المالية ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: المزيج التسويقي",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            # جمع بيانات الأجهزة
            equipment_data = []
            for row_data in self.equipment_rows:
                item = row_data['item'].get().strip()
                if item:  # فقط الصفوف التي تحتوي على بيانات
                    equipment_data.append({
                        'item': item,
                        'price': float(row_data['price'].get() or 0),
                        'quantity': float(row_data['quantity'].get() or 0),
                        'total': float(row_data['price'].get() or 0) * float(row_data['quantity'].get() or 0)
                    })
            
            # جمع بيانات المواد الخام
            materials_data = []
            for row_data in self.materials_rows:
                item = row_data['item'].get().strip()
                if item:  # فقط الصفوف التي تحتوي على بيانات
                    materials_data.append({
                        'item': item,
                        'price': float(row_data['price'].get() or 0),
                        'quantity': float(row_data['quantity'].get() or 0),
                        'total': float(row_data['price'].get() or 0) * float(row_data['quantity'].get() or 0)
                    })
            
            data = {
                'equipment': equipment_data,
                'raw_materials': materials_data
            }
            
            self.data_manager.set_data("production_requirements", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ مستلزمات الإنتاج بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("production_requirements")
            
            # تحميل بيانات الأجهزة
            equipment_data = data.get('equipment', [])
            for i, item_data in enumerate(equipment_data):
                if i < len(self.equipment_rows):
                    row_data = self.equipment_rows[i]
                    row_data['item'].delete(0, tk.END)
                    row_data['item'].insert(0, item_data.get('item', ''))
                    row_data['price'].delete(0, tk.END)
                    row_data['price'].insert(0, str(item_data.get('price', '')))
                    row_data['quantity'].delete(0, tk.END)
                    row_data['quantity'].insert(0, str(item_data.get('quantity', '')))
                else:
                    # إضافة صفوف جديدة إذا لزم الأمر
                    self.add_equipment_row()
                    row_data = self.equipment_rows[-1]
                    row_data['item'].insert(0, item_data.get('item', ''))
                    row_data['price'].insert(0, str(item_data.get('price', '')))
                    row_data['quantity'].insert(0, str(item_data.get('quantity', '')))
            
            # تحميل بيانات المواد الخام
            materials_data = data.get('raw_materials', [])
            for i, item_data in enumerate(materials_data):
                if i < len(self.materials_rows):
                    row_data = self.materials_rows[i]
                    row_data['item'].delete(0, tk.END)
                    row_data['item'].insert(0, item_data.get('item', ''))
                    row_data['price'].delete(0, tk.END)
                    row_data['price'].insert(0, str(item_data.get('price', '')))
                    row_data['quantity'].delete(0, tk.END)
                    row_data['quantity'].insert(0, str(item_data.get('quantity', '')))
                else:
                    # إضافة صفوف جديدة إذا لزم الأمر
                    self.add_materials_row()
                    row_data = self.materials_rows[-1]
                    row_data['item'].insert(0, item_data.get('item', ''))
                    row_data['price'].insert(0, str(item_data.get('price', '')))
                    row_data['quantity'].insert(0, str(item_data.get('quantity', '')))
            
            # حساب الإجماليات
            self.calculate_equipment_totals()
            self.calculate_materials_totals()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        # مسح جدول الأجهزة
        for row_data in self.equipment_rows:
            row_data['item'].delete(0, tk.END)
            row_data['price'].delete(0, tk.END)
            row_data['quantity'].delete(0, tk.END)
            row_data['total'].configure(state="normal")
            row_data['total'].delete(0, tk.END)
            row_data['total'].configure(state="readonly")
        
        # مسح جدول المواد الخام
        for row_data in self.materials_rows:
            row_data['item'].delete(0, tk.END)
            row_data['price'].delete(0, tk.END)
            row_data['quantity'].delete(0, tk.END)
            row_data['total'].configure(state="normal")
            row_data['total'].delete(0, tk.END)
            row_data['total'].configure(state="readonly")
        
        # إعادة تعيين الإجماليات
        self.equipment_total_label.configure(text="الإجمالي الكلي: 0 دينار عراقي")
        self.materials_total_label.configure(text="الإجمالي الكلي: 0 دينار عراقي")
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("financial_study")
    
    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("marketing_mix")
