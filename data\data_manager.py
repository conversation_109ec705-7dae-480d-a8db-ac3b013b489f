#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير البيانات لتطبيق دراسة الجدوى
Data Manager for Feasibility Study Application
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List

class DataManager:
    def __init__(self):
        self.data = {
            # المعلومات الشخصية
            "personal_info": {
                "owner_name": "",
                "age": "",
                "marital_status": "",
                "family_members": "",
                "education": "",
                "phone": "",
                "reference_phone": "",
                "address": ""
            },
            
            # وصف المشروع
            "project_info": {
                "project_summary": "",
                "project_characteristics": "",
                "description": "",
                "project_name": "",
                "project_location": "",
                "grant_amount": "",
                "self_funding": "",
                "total_cost": "",
                "submission_date": "",
                "importance_description": "",
                "required_skills": "",
                "community_need": "",
                "needs_license": False,
                "license_authority": "",
                "target_audience": []
            },
            
            # دراسة السوق والمنافسين
            "market_analysis": {
                # الجزء الأول
                "products_offered": "",
                "services_offered": "",
                "competitors_exist": False,
                "competitors_count": "",
                "competing_products": "",
                "competitor_profit_strategy": [],
                "price_comparison": "",
                "competitor_marketing": [],
                "competitor1_customers": "",
                "competitor2_customers": "",
                
                # الجزء الثاني
                "potential_customers": "",
                "monthly_consumption": "",
                "market_demand": "",
                "peak_seasons": "",
                "competitive_advantage": [],
                "marketing_strategy": [],
                "needs_suppliers": False,
                "suppliers_count": "",
                "suppliers_accessible": "",
                "suppliers_accessibility_reason": "",
                "supplier_prices": ""
            },
            
            # التحليل الرباعي SWOT
            "swot_analysis": {
                "strengths": "",
                "weaknesses": "",
                "opportunities": "",
                "threats": ""
            },
            
            # المزيج التسويقي
            "marketing_mix": {
                "product": {
                    "products": "",
                    "variety": "",
                    "quality": "",
                    "services": "",
                    "brand": "",
                    "packaging": ""
                },
                "price": {
                    "selling_prices": "",
                    "wholesale_retail": "",
                    "discounts": "",
                    "credit_sales": ""
                },
                "place": {
                    "distribution": "",
                    "sales_channels": "",
                    "market_coverage": "",
                    "delivery": "",
                    "location_decor": "",
                    "inventory_management": ""
                },
                "promotion": {
                    "promotion_strategy": "",
                    "advertising": "",
                    "personal_selling": "",
                    "sales_promotion": "",
                    "public_relations": "",
                    "digital_marketing": "",
                    "social_media": "",
                    "word_of_mouth": ""
                },
                "people": {
                    "project_helpers": "",
                    "employees": "",
                    "family_members": "",
                    "friends": ""
                }
            },
            
            # مستلزمات الإنتاج
            "production_requirements": {
                "equipment": [],  # قائمة الأجهزة والمعدات
                "raw_materials": []  # قائمة المواد الخام
            },
            
            # الدراسة المالية
            "financial_study": {
                # التكاليف التشغيلية
                "fixed_costs": {
                    "fixed_salaries": 0,
                    "rent": 0,
                    "maintenance": 0,
                    "marketing": 0,
                    "other_fixed": 0
                },
                "variable_costs": {
                    "raw_materials": 0,
                    "direct_labor": 0,
                    "utilities": 0,
                    "transportation": 0,
                    "other_variable": 0
                },
                
                # نفقات ما قبل التشغيل
                "pre_operation_expenses": {
                    "registration_licensing": 0,
                    "utilities_connection": 0,
                    "location_setup": 0,
                    "initial_setup": 0,
                    "other_pre_operation": 0
                },
                
                # المنتجات والإيرادات
                "products": [],  # قائمة المنتجات مع التفاصيل
                "monthly_revenues": [],  # الإيرادات الشهرية
                
                # رأس المال
                "fixed_capital": 0,
                "working_capital": 0,
                "total_project_capital": 0,
                
                # الأرباح والخسائر
                "annual_revenues": 0,
                "annual_operating_costs": 0,
                "annual_depreciation": 0,
                "net_annual_profit": 0
            },
            
            # نقطة التعادل والحاسبة
            "breakeven_calculator": {
                "monthly_fixed_costs": 0,
                "average_variable_cost_per_unit": 0,
                "average_selling_price_per_unit": 0,
                "breakeven_units": 0,
                "breakeven_revenue": 0,
                "margin_of_safety": 0,
                "contribution_margin": 0,
                "contribution_margin_ratio": 0
            },
            
            # معلومات إضافية
            "metadata": {
                "created_date": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
                "version": "1.0",
                "currency": "IQD"  # الدينار العراقي
            }
        }
        
        self.file_path = "data/feasibility_study_data.json"
    
    def get_data(self, section: str = None) -> Dict[str, Any]:
        """الحصول على البيانات"""
        if section:
            return self.data.get(section, {})
        return self.data
    
    def set_data(self, section: str, data: Dict[str, Any]):
        """تحديث البيانات"""
        if section in self.data:
            self.data[section].update(data)
            self.data["metadata"]["last_modified"] = datetime.now().isoformat()
    
    def update_field(self, section: str, field: str, value: Any):
        """تحديث حقل معين"""
        if section in self.data:
            self.data[section][field] = value
            self.data["metadata"]["last_modified"] = datetime.now().isoformat()
    
    def add_item_to_list(self, section: str, field: str, item: Dict[str, Any]):
        """إضافة عنصر إلى قائمة"""
        if section in self.data and field in self.data[section]:
            if isinstance(self.data[section][field], list):
                self.data[section][field].append(item)
                self.data["metadata"]["last_modified"] = datetime.now().isoformat()
    
    def remove_item_from_list(self, section: str, field: str, index: int):
        """حذف عنصر من قائمة"""
        if section in self.data and field in self.data[section]:
            if isinstance(self.data[section][field], list) and 0 <= index < len(self.data[section][field]):
                self.data[section][field].pop(index)
                self.data["metadata"]["last_modified"] = datetime.now().isoformat()
    
    def calculate_totals(self):
        """حساب المجاميع والنتائج المالية"""
        financial = self.data["financial_study"]
        
        # حساب مجموع التكاليف الثابتة
        total_fixed = sum(financial["fixed_costs"].values())
        
        # حساب مجموع التكاليف المتغيرة
        total_variable = sum(financial["variable_costs"].values())
        
        # حساب رأس المال العامل
        working_capital = total_fixed + total_variable
        
        # حساب مجموع نفقات ما قبل التشغيل
        total_pre_operation = sum(financial["pre_operation_expenses"].values())
        
        # حساب إجمالي رأس المال
        total_capital = financial["fixed_capital"] + working_capital + total_pre_operation
        
        # تحديث البيانات
        financial["working_capital"] = working_capital
        financial["total_project_capital"] = total_capital
        
        # حساب الإيرادات السنوية
        annual_revenues = sum(financial["monthly_revenues"]) if financial["monthly_revenues"] else 0
        financial["annual_revenues"] = annual_revenues
        
        # حساب التكاليف التشغيلية السنوية
        annual_operating_costs = (total_fixed + total_variable) * 12
        financial["annual_operating_costs"] = annual_operating_costs
        
        # حساب صافي الأرباح السنوية
        net_profit = annual_revenues - annual_operating_costs - financial["annual_depreciation"]
        financial["net_annual_profit"] = net_profit
        
        return {
            "total_fixed_costs": total_fixed,
            "total_variable_costs": total_variable,
            "working_capital": working_capital,
            "total_pre_operation": total_pre_operation,
            "total_capital": total_capital,
            "annual_revenues": annual_revenues,
            "annual_operating_costs": annual_operating_costs,
            "net_annual_profit": net_profit
        }
    
    def calculate_breakeven(self):
        """حساب نقطة التعادل"""
        try:
            financial = self.data["financial_study"]
            breakeven = self.data["breakeven_calculator"]
            
            # الحصول على البيانات المطلوبة
            fixed_costs = sum(financial["fixed_costs"].values())
            
            # حساب متوسط التكلفة المتغيرة للوحدة
            if financial["products"]:
                total_variable_cost = sum(product.get("unit_cost", 0) for product in financial["products"])
                avg_variable_cost = total_variable_cost / len(financial["products"])
            else:
                avg_variable_cost = breakeven.get("average_variable_cost_per_unit", 0)
            
            # حساب متوسط سعر البيع للوحدة
            if financial["products"]:
                total_selling_price = sum(product.get("selling_price", 0) for product in financial["products"])
                avg_selling_price = total_selling_price / len(financial["products"])
            else:
                avg_selling_price = breakeven.get("average_selling_price_per_unit", 0)
            
            # حساب نقطة التعادل
            if avg_selling_price > avg_variable_cost:
                breakeven_units = fixed_costs / (avg_selling_price - avg_variable_cost)
                breakeven_revenue = breakeven_units * avg_selling_price
                contribution_margin = avg_selling_price - avg_variable_cost
                contribution_margin_ratio = contribution_margin / avg_selling_price if avg_selling_price > 0 else 0
            else:
                breakeven_units = 0
                breakeven_revenue = 0
                contribution_margin = 0
                contribution_margin_ratio = 0
            
            # تحديث البيانات
            breakeven.update({
                "monthly_fixed_costs": fixed_costs,
                "average_variable_cost_per_unit": avg_variable_cost,
                "average_selling_price_per_unit": avg_selling_price,
                "breakeven_units": breakeven_units,
                "breakeven_revenue": breakeven_revenue,
                "contribution_margin": contribution_margin,
                "contribution_margin_ratio": contribution_margin_ratio
            })
            
            return breakeven
            
        except Exception as e:
            print(f"خطأ في حساب نقطة التعادل: {e}")
            return self.data["breakeven_calculator"]
    
    def save_to_file(self, file_path: str = None):
        """حفظ البيانات في ملف JSON"""
        if file_path is None:
            file_path = self.file_path
        
        # التأكد من وجود المجلد
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # حفظ البيانات
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, ensure_ascii=False, indent=2)
    
    def load_from_file(self, file_path: str = None):
        """تحميل البيانات من ملف JSON"""
        if file_path is None:
            file_path = self.file_path
        
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
                # دمج البيانات المحملة مع البيانات الافتراضية
                self._merge_data(self.data, loaded_data)
    
    def _merge_data(self, default_data: Dict, loaded_data: Dict):
        """دمج البيانات المحملة مع البيانات الافتراضية"""
        for key, value in loaded_data.items():
            if key in default_data:
                if isinstance(value, dict) and isinstance(default_data[key], dict):
                    self._merge_data(default_data[key], value)
                else:
                    default_data[key] = value
    
    def export_to_dict(self) -> Dict[str, Any]:
        """تصدير البيانات كقاموس"""
        return self.data.copy()
    
    def get_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص البيانات"""
        summary = {
            "project_name": self.data["project_info"]["project_name"],
            "owner_name": self.data["personal_info"]["owner_name"],
            "total_cost": self.data["project_info"]["total_cost"],
            "grant_amount": self.data["project_info"]["grant_amount"],
            "net_profit": self.data["financial_study"]["net_annual_profit"],
            "breakeven_units": self.data["breakeven_calculator"]["breakeven_units"],
            "last_modified": self.data["metadata"]["last_modified"]
        }
        return summary
