#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة دراسة السوق والمنافسين
Market Analysis Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk

class MarketAnalysisFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Arial", 12)  # استخدام Arial كما هو مطلوب
            self.arabic_font_bold = ("Arial", 14, "bold")
            self.arabic_font_large = ("Arial", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير مع خلفية بيضاء
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self, 
            label_text="📊 دراسة السوق والمنافسين",
            fg_color=("#ffffff", "#2b2b2b")
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="دراسة السوق والمنافسين",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # الجزء الأول من دراسة السوق
        self.create_part_one()
        
        # الجزء الثاني من دراسة السوق
        self.create_part_two()
        
        # أزرار التحكم
        self.create_control_buttons()
        
        # إنشاء متغيرات الحفظ
        self.fields = {}
        self.checkbox_vars = {}
        self.radio_vars = {}
    
    def create_part_one(self):
        """إنشاء الجزء الأول من دراسة السوق"""
        # إطار الجزء الأول
        part1_frame = ctk.CTkFrame(self.scrollable_frame, fg_color=("#ffffff", "#2b2b2b"))
        part1_frame.pack(fill="x", padx=20, pady=10)
        
        part1_title = ctk.CTkLabel(
            part1_frame,
            text="الجزء الأول - تحليل المنتجات والمنافسين",
            font=self.arabic_font_bold,
            text_color=("#1f538d", "#ffffff")
        )
        part1_title.pack(pady=10)
        
        # ماذا ستقدم في مشروعك؟
        offering_frame = tk.LabelFrame(
            part1_frame,
            text="ماذا ستقدم في مشروعك؟",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        offering_frame.pack(fill="x", padx=20, pady=10)
        
        # منتجات
        products_frame = tk.Frame(offering_frame, bg="white")
        products_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(
            products_frame,
            text="◼️ منتجات (اذكرها):",
            font=self.arabic_font_bold,
            bg="white",
            anchor="e"
        ).pack(side="right", padx=10)
        
        self.products_entry = tk.Entry(
            products_frame,
            font=self.arabic_font,
            width=40,
            justify="right"
        )
        self.products_entry.pack(side="right", padx=10)
        
        # خدمات
        services_frame = tk.Frame(offering_frame, bg="white")
        services_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(
            services_frame,
            text="◼️ خدمات (اذكرها):",
            font=self.arabic_font_bold,
            bg="white",
            anchor="e"
        ).pack(side="right", padx=10)
        
        self.services_entry = tk.Entry(
            services_frame,
            font=self.arabic_font,
            width=40,
            justify="right"
        )
        self.services_entry.pack(side="right", padx=10)
        
        # هل يوجد منافسين؟
        competitors_frame = tk.LabelFrame(
            part1_frame,
            text="هل يوجد منافسين يبيعون نفس المنتج أو منتج شبيه في منطقة مشروعك؟",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        competitors_frame.pack(fill="x", padx=20, pady=10)
        
        self.competitors_var = tk.StringVar(value="لا")
        
        competitors_options_frame = tk.Frame(competitors_frame, bg="white")
        competitors_options_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Radiobutton(
            competitors_options_frame,
            text="نعم – كم عددهم؟",
            font=self.arabic_font,
            variable=self.competitors_var,
            value="نعم",
            bg="white",
            command=self.toggle_competitors_count
        ).pack(side="right", padx=10)
        
        self.competitors_count_entry = tk.Entry(
            competitors_options_frame,
            font=self.arabic_font,
            width=10,
            justify="right",
            state="disabled"
        )
        self.competitors_count_entry.pack(side="right", padx=5)
        
        tk.Radiobutton(
            competitors_options_frame,
            text="لا",
            font=self.arabic_font,
            variable=self.competitors_var,
            value="لا",
            bg="white",
            command=self.toggle_competitors_count
        ).pack(side="right", padx=10)
        
        # المنتجات المنافسة
        competing_products_frame = tk.LabelFrame(
            part1_frame,
            text="ما هي المنتجات المنافسة أو الشبيهة التي يبيعها المنافسون؟",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        competing_products_frame.pack(fill="x", padx=20, pady=10)
        
        self.competing_products_entry = tk.Entry(
            competing_products_frame,
            font=self.arabic_font,
            width=60,
            justify="right"
        )
        self.competing_products_entry.pack(padx=10, pady=10)
        
        # كيف يحاول المنافسون تحقيق الربح؟
        profit_strategy_frame = tk.LabelFrame(
            part1_frame,
            text="كيف يحاول المنافسون أن يحققوا الربح؟",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        profit_strategy_frame.pack(fill="x", padx=20, pady=10)
        
        self.profit_strategy_vars = {}
        profit_options = [
            ("low_price", "من خلال السعر المنخفض"),
            ("quality", "من خلال جودة المنتج"),
            ("service", "من خلال الخدمة المميزة"),
            ("low_cost", "من خلال التكلفة المنخفضة")
        ]
        
        for key, text in profit_options:
            var = tk.BooleanVar()
            self.profit_strategy_vars[key] = var
            tk.Checkbutton(
                profit_strategy_frame,
                text=f"◼️ {text}",
                font=self.arabic_font,
                variable=var,
                bg="white",
                anchor="e"
            ).pack(anchor="e", padx=20, pady=2)
        
        # أخرى
        other_profit_frame = tk.Frame(profit_strategy_frame, bg="white")
        other_profit_frame.pack(fill="x", padx=20, pady=5)
        
        self.other_profit_var = tk.BooleanVar()
        tk.Checkbutton(
            other_profit_frame,
            text="◼️ أخرى (اذكرها):",
            font=self.arabic_font,
            variable=self.other_profit_var,
            bg="white",
            command=self.toggle_other_profit
        ).pack(side="right", padx=10)
        
        self.other_profit_entry = tk.Entry(
            other_profit_frame,
            font=self.arabic_font,
            width=30,
            justify="right",
            state="disabled"
        )
        self.other_profit_entry.pack(side="right", padx=10)
        
        # مقارنة الأسعار
        price_comparison_frame = tk.LabelFrame(
            part1_frame,
            text="ما هي أسعار المنافسين مقارنة بالسعر الذي تنوي البيع به؟",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        price_comparison_frame.pack(fill="x", padx=20, pady=10)
        
        self.price_comparison_var = tk.StringVar()
        price_options = [
            ("same", "نفس سعر البيع الذي سأبيع به تقريبًا"),
            ("higher", "أسعار المنافسين أعلى من سعري"),
            ("lower", "أسعار المنافسين أقل من سعري")
        ]
        
        for value, text in price_options:
            tk.Radiobutton(
                price_comparison_frame,
                text=f"◼️ {text}",
                font=self.arabic_font,
                variable=self.price_comparison_var,
                value=value,
                bg="white",
                anchor="e"
            ).pack(anchor="e", padx=20, pady=2)
    
    def create_part_two(self):
        """إنشاء الجزء الثاني من دراسة السوق"""
        # إطار الجزء الثاني
        part2_frame = ctk.CTkFrame(self.scrollable_frame, fg_color=("#ffffff", "#2b2b2b"))
        part2_frame.pack(fill="x", padx=20, pady=10)
        
        part2_title = ctk.CTkLabel(
            part2_frame,
            text="الجزء الثاني - تحليل السوق المستهدف",
            font=self.arabic_font_bold,
            text_color=("#1f538d", "#ffffff")
        )
        part2_title.pack(pady=10)
        
        # جدول خصائص المشروع
        characteristics_frame = tk.LabelFrame(
            part2_frame,
            text="خصائص المشروع",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        characteristics_frame.pack(fill="x", padx=20, pady=10)
        
        # إنشاء الحقول في شكل جدول
        characteristics_fields = [
            ("potential_customers", "كم أعداد الزبائن المحتمل أن يشتروا منتجاتك (العدد الكلي للزبائن المحتملين)"),
            ("monthly_consumption", "كم معدل استهلاكهم للمنتج في الشهر"),
            ("peak_seasons", "ما هي المواسم التي يشتد فيها البيع لمنتجاتك أو خدماتك (مثل: الأعياد، رمضان، الصيف...)")
        ]
        
        self.characteristics_entries = {}
        for key, label in characteristics_fields:
            field_frame = tk.Frame(characteristics_frame, bg="white")
            field_frame.pack(fill="x", padx=10, pady=5)
            
            tk.Label(
                field_frame,
                text=label,
                font=self.arabic_font,
                bg="white",
                anchor="e",
                wraplength=400
            ).pack(side="right", padx=10)
            
            entry = tk.Entry(
                field_frame,
                font=self.arabic_font,
                width=30,
                justify="right"
            )
            entry.pack(side="left", padx=10)
            self.characteristics_entries[key] = entry
        
        # هل المنتجات الشبيهة تلبي احتياج السوق؟
        market_demand_frame = tk.LabelFrame(
            part2_frame,
            text="هل المنتجات الشبيهة تلبي احتياج السوق أم أن هناك طلبًا كبيرًا على المنتجات والمعروض أقل:",
            font=self.arabic_font_bold,
            bg="white",
            fg="#1f538d"
        )
        market_demand_frame.pack(fill="x", padx=20, pady=10)
        
        self.market_demand_var = tk.StringVar()
        demand_options = [
            ("high_demand", "هناك طلب كبير على المنتجات والمعروض أقل"),
            ("oversupply", "المعروض من المنتجات الشبيهة أكبر من الطلب"),
            ("balanced", "المعروض في السوق هو نفس حاجة السوق (العرض = الطلب)")
        ]
        
        for value, text in demand_options:
            tk.Radiobutton(
                market_demand_frame,
                text=f"☐ {text}",
                font=self.arabic_font,
                variable=self.market_demand_var,
                value=value,
                bg="white",
                anchor="e"
            ).pack(anchor="e", padx=20, pady=2)
    
    def toggle_competitors_count(self):
        """تفعيل/إلغاء تفعيل حقل عدد المنافسين"""
        if self.competitors_var.get() == "نعم":
            self.competitors_count_entry.configure(state="normal")
        else:
            self.competitors_count_entry.configure(state="disabled")
            self.competitors_count_entry.delete(0, tk.END)
    
    def toggle_other_profit(self):
        """تفعيل/إلغاء تفعيل حقل الربح الآخر"""
        if self.other_profit_var.get():
            self.other_profit_entry.configure(state="normal")
        else:
            self.other_profit_entry.configure(state="disabled")
            self.other_profit_entry.delete(0, tk.END)
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ دراسة السوق",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: التحليل الرباعي ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: وصف المشروع",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {
                # الجزء الأول
                "products_offered": self.products_entry.get(),
                "services_offered": self.services_entry.get(),
                "competitors_exist": self.competitors_var.get() == "نعم",
                "competitors_count": self.competitors_count_entry.get(),
                "competing_products": self.competing_products_entry.get(),
                "price_comparison": self.price_comparison_var.get(),
                
                # الجزء الثاني
                "market_demand": self.market_demand_var.get()
            }
            
            # حفظ استراتيجيات الربح
            profit_strategies = []
            for key, var in self.profit_strategy_vars.items():
                if var.get():
                    strategies = {
                        "low_price": "من خلال السعر المنخفض",
                        "quality": "من خلال جودة المنتج",
                        "service": "من خلال الخدمة المميزة",
                        "low_cost": "من خلال التكلفة المنخفضة"
                    }
                    profit_strategies.append(strategies[key])
            
            if self.other_profit_var.get() and self.other_profit_entry.get():
                profit_strategies.append(f"أخرى: {self.other_profit_entry.get()}")
            
            data["competitor_profit_strategy"] = profit_strategies
            
            # حفظ خصائص المشروع
            for key, entry in self.characteristics_entries.items():
                data[key] = entry.get()
            
            self.data_manager.set_data("market_analysis", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ دراسة السوق بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("market_analysis")
            
            # تحميل الحقول الأساسية
            self.products_entry.delete(0, tk.END)
            self.products_entry.insert(0, data.get("products_offered", ""))
            
            self.services_entry.delete(0, tk.END)
            self.services_entry.insert(0, data.get("services_offered", ""))
            
            # تحميل معلومات المنافسين
            competitors_exist = data.get("competitors_exist", False)
            self.competitors_var.set("نعم" if competitors_exist else "لا")
            self.toggle_competitors_count()
            
            self.competitors_count_entry.delete(0, tk.END)
            self.competitors_count_entry.insert(0, data.get("competitors_count", ""))
            
            # تحميل باقي البيانات...
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        # مسح الحقول النصية
        self.products_entry.delete(0, tk.END)
        self.services_entry.delete(0, tk.END)
        self.competing_products_entry.delete(0, tk.END)
        self.competitors_count_entry.delete(0, tk.END)
        self.other_profit_entry.delete(0, tk.END)
        
        # إعادة تعيين المتغيرات
        self.competitors_var.set("لا")
        self.price_comparison_var.set("")
        self.market_demand_var.set("")
        self.other_profit_var.set(False)
        
        for var in self.profit_strategy_vars.values():
            var.set(False)
        
        for entry in self.characteristics_entries.values():
            entry.delete(0, tk.END)
        
        self.toggle_competitors_count()
        self.toggle_other_profit()
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("swot_analysis")
    
    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("project_info")
