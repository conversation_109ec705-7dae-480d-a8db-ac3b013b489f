#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المزيج التسويقي
Marketing Mix Interface (4Ps + 1)
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk

class MarketingMixFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="🧃 المزيج التسويقي")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="عناصر المزيج التسويقي - Marketing Mix (4Ps + 1)",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # شرح المزيج التسويقي
        explanation_frame = ctk.CTkFrame(self.scrollable_frame)
        explanation_frame.pack(fill="x", padx=20, pady=10)
        
        explanation_text = """
المزيج التسويقي يتكون من 5 عناصر أساسية:
• المنتج (Product): ما تقدمه من منتجات أو خدمات
• السعر (Price): استراتيجية التسعير والخصومات
• المكان (Place): قنوات التوزيع والمكان
• الترويج (Promotion): طرق التسويق والإعلان
• الناس (People): الفريق والموظفين المساعدين
        """
        
        explanation_label = ctk.CTkLabel(
            explanation_frame,
            text=explanation_text,
            font=self.arabic_font,
            text_color=("#059669", "#10b981"),
            justify="right",
            wraplength=600
        )
        explanation_label.pack(pady=15)
        
        # إنشاء الأقسام الخمسة
        self.fields = {}
        
        # 1. المنتج (Product)
        self.create_product_section()
        
        # 2. السعر (Price)
        self.create_price_section()
        
        # 3. المكان (Place)
        self.create_place_section()
        
        # 4. الترويج (Promotion)
        self.create_promotion_section()
        
        # 5. الناس (People)
        self.create_people_section()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_product_section(self):
        """إنشاء قسم المنتج"""
        product_frame = ctk.CTkFrame(self.scrollable_frame)
        product_frame.pack(fill="x", padx=20, pady=10)
        
        product_title = ctk.CTkLabel(
            product_frame,
            text="🛍️ المنتج (Product)",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        product_title.pack(pady=(15, 10))
        
        # حقول المنتج
        product_fields = [
            ("products", "ما هي المنتجات التي ستقدمها؟"),
            ("variety", "هل فيها تنوع؟"),
            ("quality", "هل جودتها عالية؟"),
            ("services", "هل ستقدم خدمات مصاحبة؟"),
            ("brand", "هل ستميزها بعلامة تجارية؟"),
            ("packaging", "هل ستغلفها بطريقة مميزة؟")
        ]
        
        self.fields["product"] = {}
        for field_key, field_label in product_fields:
            self.create_text_field(product_frame, f"product_{field_key}", field_label, "product")
    
    def create_price_section(self):
        """إنشاء قسم السعر"""
        price_frame = ctk.CTkFrame(self.scrollable_frame)
        price_frame.pack(fill="x", padx=20, pady=10)
        
        price_title = ctk.CTkLabel(
            price_frame,
            text="💰 السعر (Price)",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        price_title.pack(pady=(15, 10))
        
        # حقول السعر
        price_fields = [
            ("selling_prices", "ما هي أسعار البيع؟"),
            ("wholesale_retail", "هل هناك أسعار جملة وتجزئة؟"),
            ("discounts", "هل ستقدم تخفيضات أو خصومات؟ (نسبة الخصم؟)"),
            ("credit_sales", "هل ستسمح بالبيع الآجل؟")
        ]
        
        self.fields["price"] = {}
        for field_key, field_label in price_fields:
            self.create_text_field(price_frame, f"price_{field_key}", field_label, "price")
    
    def create_place_section(self):
        """إنشاء قسم المكان"""
        place_frame = ctk.CTkFrame(self.scrollable_frame)
        place_frame.pack(fill="x", padx=20, pady=10)
        
        place_title = ctk.CTkLabel(
            place_frame,
            text="📍 المكان (Place)",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        place_title.pack(pady=(15, 10))
        
        # حقول المكان
        place_fields = [
            ("distribution", "كيف ستوزع المنتجات؟"),
            ("sales_channels", "ما هي قنوات البيع والتوزيع؟"),
            ("market_coverage", "ما مدى التغطية السوقية؟"),
            ("delivery", "هل يوجد توصيل ونقل؟"),
            ("location_decor", "كيف سيكون الموقع والديكور؟"),
            ("inventory_management", "كيف ستدير المخزون والإمدادات والموردين؟")
        ]
        
        self.fields["place"] = {}
        for field_key, field_label in place_fields:
            self.create_text_field(place_frame, f"place_{field_key}", field_label, "place")
    
    def create_promotion_section(self):
        """إنشاء قسم الترويج"""
        promotion_frame = ctk.CTkFrame(self.scrollable_frame)
        promotion_frame.pack(fill="x", padx=20, pady=10)
        
        promotion_title = ctk.CTkLabel(
            promotion_frame,
            text="📢 الترويج (Promotion)",
            font=self.arabic_font_bold,
            text_color=("#8b5cf6", "#8b5cf6")
        )
        promotion_title.pack(pady=(15, 10))
        
        # حقول الترويج
        promotion_fields = [
            ("promotion_strategy", "كيف ستروج للمشروع؟"),
            ("advertising", "الإعلان"),
            ("personal_selling", "البيع الشخصي"),
            ("sales_promotion", "العروض الترويجية"),
            ("public_relations", "العلاقات العامة"),
            ("digital_marketing", "التسويق الإلكتروني"),
            ("social_media", "وسائل التواصل الاجتماعي"),
            ("word_of_mouth", "المعارف والأصدقاء")
        ]
        
        self.fields["promotion"] = {}
        for field_key, field_label in promotion_fields:
            self.create_text_field(promotion_frame, f"promotion_{field_key}", field_label, "promotion")
    
    def create_people_section(self):
        """إنشاء قسم الناس"""
        people_frame = ctk.CTkFrame(self.scrollable_frame)
        people_frame.pack(fill="x", padx=20, pady=10)
        
        people_title = ctk.CTkLabel(
            people_frame,
            text="👥 الناس (People)",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        people_title.pack(pady=(15, 10))
        
        # حقول الناس
        people_fields = [
            ("project_helpers", "من سيساعدك في المشروع؟"),
            ("employees", "موظفين"),
            ("family_members", "أفراد من العائلة"),
            ("friends", "أصدقاء")
        ]
        
        self.fields["people"] = {}
        for field_key, field_label in people_fields:
            self.create_text_field(people_frame, f"people_{field_key}", field_label, "people")
    
    def create_text_field(self, parent, field_key, field_label, section):
        """إنشاء حقل نص"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", padx=20, pady=5)
        
        # تسمية الحقل
        label = ctk.CTkLabel(
            field_frame,
            text=field_label,
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        label.pack(side="right", padx=(0, 20))
        
        # حقل الإدخال
        entry = ctk.CTkEntry(
            field_frame,
            font=self.arabic_font,
            width=400,
            justify="right"
        )
        entry.pack(side="right", padx=20)
        
        # حفظ المرجع
        clean_key = field_key.replace(f"{section}_", "")
        self.fields[section][clean_key] = entry
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ المزيج التسويقي",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: مستلزمات الإنتاج ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: التحليل الرباعي",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {}
            
            for section, fields in self.fields.items():
                data[section] = {}
                for field_key, entry in fields.items():
                    data[section][field_key] = entry.get()
            
            self.data_manager.set_data("marketing_mix", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ المزيج التسويقي بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("marketing_mix")
            
            for section, fields in self.fields.items():
                section_data = data.get(section, {})
                for field_key, entry in fields.items():
                    value = section_data.get(field_key, "")
                    entry.delete(0, tk.END)
                    entry.insert(0, value)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        for section, fields in self.fields.items():
            for entry in fields.values():
                entry.delete(0, tk.END)
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("production_requirements")
    
    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("swot_analysis")
