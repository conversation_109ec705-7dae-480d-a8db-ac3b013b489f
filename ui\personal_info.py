#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المعلومات الشخصية
Personal Information Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk

class PersonalInfoFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="🧍‍♂️ المعلومات الشخصية")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="المعلومات الشخصية لصاحب المشروع",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الجدول
        table_frame = ctk.CTkFrame(self.scrollable_frame)
        table_frame.pack(fill="x", padx=20, pady=10)
        
        # إنشاء الحقول
        self.fields = {}
        
        # قائمة الحقول
        field_list = [
            ("owner_name", "إسم صاحب/ة المشروع"),
            ("age", "العمر"),
            ("marital_status", "الحالة الإجتماعية"),
            ("family_members", "عدد أفراد الأسرة"),
            ("education", "المؤهل العلمي"),
            ("phone", "رقم الهاتف"),
            ("reference_phone", "رقم هاتف شخص معرف"),
            ("address", "مكان السكن")
        ]
        
        # إنشاء الحقول في شكل جدول
        for i, (field_key, field_label) in enumerate(field_list):
            # إطار الصف
            row_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
            row_frame.pack(fill="x", padx=10, pady=5)
            
            # تسمية الحقل
            label = ctk.CTkLabel(
                row_frame,
                text=field_label,
                font=self.arabic_font_bold,
                width=200,
                anchor="e"
            )
            label.pack(side="right", padx=(0, 20))
            
            # حقل الإدخال
            if field_key in ["marital_status", "education"]:
                # قائمة منسدلة للحالة الاجتماعية والمؤهل العلمي
                if field_key == "marital_status":
                    values = ["أعزب/عزباء", "متزوج/متزوجة", "مطلق/مطلقة", "أرمل/أرملة"]
                else:  # education
                    values = ["ابتدائية", "متوسطة", "إعدادية", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه", "أخرى"]
                
                entry = ctk.CTkComboBox(
                    row_frame,
                    values=values,
                    font=self.arabic_font,
                    width=300,
                    justify="right"
                )
            else:
                # حقل نص عادي
                entry = ctk.CTkEntry(
                    row_frame,
                    font=self.arabic_font,
                    width=300,
                    justify="right"
                )
            
            entry.pack(side="right", padx=20)
            self.fields[field_key] = entry
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ المعلومات الشخصية",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)
        
        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)
        
        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: وصف المشروع ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)
        
        # معلومات إضافية
        info_frame = ctk.CTkFrame(self.scrollable_frame)
        info_frame.pack(fill="x", padx=20, pady=10)
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="💡 نصيحة: تأكد من إدخال جميع المعلومات بدقة حيث ستظهر في التقرير النهائي",
            font=self.arabic_font,
            text_color=("#059669", "#10b981"),
            wraplength=600
        )
        info_label.pack(pady=15)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {}
            for field_key, widget in self.fields.items():
                if isinstance(widget, ctk.CTkComboBox):
                    data[field_key] = widget.get()
                else:
                    data[field_key] = widget.get()
            
            self.data_manager.set_data("personal_info", data)
            
            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ المعلومات الشخصية بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            
            # إزالة الرسالة بعد 3 ثوان
            self.after(3000, success_label.destroy)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("personal_info")
            
            for field_key, widget in self.fields.items():
                value = data.get(field_key, "")
                if isinstance(widget, ctk.CTkComboBox):
                    widget.set(value)
                else:
                    widget.delete(0, tk.END)
                    widget.insert(0, value)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def clear_fields(self):
        """مسح جميع الحقول"""
        for widget in self.fields.values():
            if isinstance(widget, ctk.CTkComboBox):
                widget.set("")
            else:
                widget.delete(0, tk.END)
    
    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        # حفظ البيانات أولاً
        self.save_data()
        
        # الانتقال لقسم وصف المشروع
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("project_info")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        required_fields = ["owner_name", "age", "phone"]
        missing_fields = []
        
        for field_key in required_fields:
            widget = self.fields[field_key]
            value = widget.get() if isinstance(widget, ctk.CTkComboBox) else widget.get()
            if not value.strip():
                missing_fields.append(field_key)
        
        return len(missing_fields) == 0, missing_fields
