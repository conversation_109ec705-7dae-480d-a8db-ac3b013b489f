# تطبيق دراسة الجدوى الشامل - الإصدار 2.0
## Comprehensive Feasibility Study Application - Version 2.0

تطبيق شامل ومتكامل لإعداد دراسة الجدوى الاقتصادية مع حساب نقطة التعادل وتوليد التقارير المفصلة باللغة العربية.

## 🆕 الجديد في الإصدار 2.0

### ✨ تحسينات الواجهة
- **واجهة عربية RTL كاملة** - جميع العناصر محاذاة لليمين
- **تصميم احترافي عصري** - ألوان متدرجة وتنسيق متقدم
- **أيقونات تعبيرية** - لكل قسم أيقونة مميزة
- **خطوط عربية محسنة** - دعم خط Tahoma للوضوح الأمثل
- **تخطيط شبكي منظم** - ترتيب الأقسام في شبكة 3×3

### 📊 أقسام جديدة ومحسنة
- **دراسة السوق والمنافسين** - تحليل شامل للسوق
- **التحليل الرباعي SWOT** - نقاط القوة والضعف والفرص والتهديدات
- **المزيج التسويقي 4Ps+1** - استراتيجية التسويق المتكاملة
- **مستلزمات الإنتاج** - جداول تفاعلية للأجهزة والمواد الخام
- **تحسينات الدراسة المالية** - رأس المال والنفقات التأسيسية

### 🔧 مميزات تقنية جديدة
- **حسابات تلقائية** - في جداول مستلزمات الإنتاج
- **تحميل البيانات الذكي** - استرجاع البيانات المحفوظة تلقائياً
- **تقارير محسنة** - ملخص شامل يتضمن جميع الأقسام
- **نصائح وإرشادات** - في كل قسم لمساعدة المستخدم

## المميزات الرئيسية

### 🧍‍♂️ المعلومات الشخصية
- إدخال بيانات صاحب المشروع
- المعلومات الأساسية والتواصل
- حفظ وتحميل البيانات

### 🧾 وصف المشروع
- تفاصيل المشروع الكاملة
- معلومات التمويل والتكلفة
- وصف الأهمية والمهارات المطلوبة
- تحديد الفئة المستهدفة

### 📊 دراسة السوق والمنافسين
- تحليل المنتجات والخدمات
- دراسة المنافسين وأسعارهم
- تقييم الطلب والعرض في السوق
- استراتيجيات التسويق

### 📈 التحليل الرباعي SWOT
- نقاط القوة (Strengths)
- نقاط الضعف (Weaknesses)  
- الفرص (Opportunities)
- التهديدات (Threats)
- مصفوفة SWOT المرئية

### 🧃 المزيج التسويقي (4Ps + 1)
- المنتج (Product)
- السعر (Price)
- المكان (Place)
- الترويج (Promotion)
- الناس (People)

### 🛠️ مستلزمات الإنتاج
- جداول الأجهزة والمعدات
- جداول المواد الخام
- حساب التكاليف التلقائي
- إدارة الجداول بسهولة

### 📘 الدراسة المالية
- التكاليف الثابتة والمتغيرة
- النفقات التأسيسية
- حساب رأس المال
- جداول المنتجات والإيرادات
- الأرباح والخسائر السنوية

### 📍 نقطة التعادل وحاسبة الاحتساب
- حساب نقطة التعادل بدقة
- هامش المساهمة
- حاسبة الاحتساب الآلية
- تحليل الجدوى المالية
- التوصيات والمؤشرات

### 📋 الملخص والتقرير النهائي
- ملخص شامل للمشروع
- تقييم الجدوى الشامل
- التوصيات والخطوات التالية
- تصدير التقارير (PDF, Word, Excel)
- طباعة التقارير

## متطلبات التشغيل

### النسخة الكاملة (main.py)
```bash
pip install customtkinter
python main.py
```

### النسخة المبسطة (main_simple.py)
```bash
python main_simple.py
```
*تعمل مع Python العادي بدون مكتبات إضافية*

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
python main_simple.py
```

### 2. إدخال البيانات
- ابدأ بالمعلومات الشخصية
- انتقل لوصف المشروع
- أدخل البيانات المالية
- احسب نقطة التعادل

### 3. توليد التقرير
- اذهب للملخص والتقرير
- راجع النتائج والتوصيات
- صدّر التقرير بالصيغة المطلوبة

## هيكل المشروع

```
saif/
├── main.py                     # النسخة الكاملة (CustomTkinter)
├── main_simple.py              # النسخة المبسطة (Tkinter العادي)
├── data/
│   ├── __init__.py
│   └── data_manager.py         # مدير البيانات
├── ui/
│   ├── __init__.py
│   ├── personal_info.py        # واجهة المعلومات الشخصية
│   ├── project_info.py         # واجهة وصف المشروع
│   ├── market_analysis.py      # واجهة دراسة السوق
│   ├── swot_analysis.py        # واجهة التحليل الرباعي
│   ├── marketing_mix.py        # واجهة المزيج التسويقي
│   ├── production_requirements.py  # واجهة مستلزمات الإنتاج
│   ├── financial_study.py      # واجهة الدراسة المالية
│   ├── breakeven_calculator.py # واجهة نقطة التعادل
│   └── summary_report.py       # واجهة الملخص والتقرير
├── assets/                     # الصور والأيقونات
├── reports/                    # التقارير المُصدّرة
└── README.md                   # هذا الملف
```

## المميزات التقنية

### 🎨 التصميم والواجهة
- **واجهة عربية RTL كاملة** - محاذاة صحيحة لجميع العناصر
- **تصميم احترافي متدرج** - ألوان عصرية ومتناسقة
- **أيقونات تعبيرية** - لكل قسم ووظيفة
- **خطوط عربية محسنة** - Tahoma وArial Unicode MS
- **تخطيط شبكي منظم** - ترتيب منطقي للأقسام
- **تأثيرات بصرية** - إطارات مرفوعة وظلال ناعمة

### 💾 إدارة البيانات
- حفظ تلقائي للبيانات
- تصدير JSON
- استيراد وتصدير البيانات
- نسخ احتياطية

### 🧮 الحسابات المالية
- حسابات دقيقة لنقطة التعادل
- تحليل الربحية
- مؤشرات الأداء المالي
- توصيات ذكية

### 📊 التقارير
- تقارير شاملة ومفصلة
- تصدير متعدد الصيغ
- طباعة مباشرة
- ملخص تنفيذي

## الصيغ المالية المستخدمة

### نقطة التعادل
```
نقطة التعادل (وحدات) = التكاليف الثابتة ÷ (سعر البيع - التكلفة المتغيرة للوحدة)
```

### هامش المساهمة
```
هامش المساهمة = سعر البيع - التكلفة المتغيرة للوحدة
نسبة هامش المساهمة = هامش المساهمة ÷ سعر البيع × 100
```

### الربحية
```
الربح الشهري = الإيرادات الشهرية - التكاليف الشهرية
الربح السنوي = الربح الشهري × 12 - الإهلاك السنوي
```

## العملة المستخدمة

التطبيق يستخدم **الدينار العراقي (IQD)** كعملة أساسية مع إمكانية إضافة الدولار الأمريكي.

## الدعم والمساعدة

### المشاكل الشائعة

**1. خطأ في تشغيل النسخة الكاملة:**
```bash
# استخدم النسخة المبسطة
python main_simple.py
```

**2. مشكلة في الخطوط العربية:**
- التطبيق يستخدم خط Tajawal افتراضياً
- في حالة عدم التوفر يستخدم Arial Unicode MS

**3. مشكلة في حفظ البيانات:**
- تأكد من وجود صلاحيات الكتابة في المجلد
- سيتم إنشاء مجلد `data` تلقائياً

### نصائح للاستخدام الأمثل

1. **ابدأ بالترتيب:** املأ الأقسام بالترتيب المنطقي
2. **احفظ بانتظام:** استخدم زر الحفظ بعد كل قسم
3. **راجع البيانات:** تأكد من صحة الأرقام المالية
4. **استخدم التوصيات:** اتبع التوصيات المقترحة في التقرير

## الترخيص

هذا التطبيق مجاني للاستخدام الشخصي والتعليمي.

## المطور

تم تطوير هذا التطبيق باستخدام:
- Python 3.x
- Tkinter / CustomTkinter
- JSON لحفظ البيانات

---

**ملاحظة:** يُنصح بمراجعة دراسة الجدوى مع خبراء في المجال قبل اتخاذ قرارات الاستثمار النهائية.
