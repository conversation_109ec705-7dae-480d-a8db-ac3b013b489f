#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق دراسة الجدوى الشامل
Comprehensive Feasibility Study Application
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

# استيراد الواجهات المختلفة
from ui.personal_info import PersonalInfoFrame
from ui.project_info import ProjectInfoFrame
from ui.market_analysis import MarketAnalysisFrame
from ui.swot_analysis import SWOTAnalysisFrame
from ui.marketing_mix import MarketingMixFrame
from ui.production_requirements import ProductionRequirementsFrame
from ui.financial_study import FinancialStudyFrame
from ui.breakeven_calculator import BreakevenCalculatorFrame
from ui.summary_report import SummaryReportFrame
from data.data_manager import DataManager

# إعداد المظهر
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class FeasibilityStudyApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("تطبيق دراسة الجدوى الشامل - Comprehensive Feasibility Study")
        self.root.geometry("1400x900")
        self.root.configure(fg_color=("#f0f0f0", "#2b2b2b"))
        
        # إعداد الخط العربي
        self.setup_fonts()
        
        # مدير البيانات
        self.data_manager = DataManager()
        
        # إعداد الواجهة الرئيسية
        self.setup_main_interface()
        
        # تحميل البيانات المحفوظة
        self.load_saved_data()
        
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة استخدام خط Tajawal
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            # استخدام خط بديل
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إطار الشريط الجانبي (القائمة)
        self.sidebar_frame = ctk.CTkFrame(self.main_frame, width=300)
        self.sidebar_frame.pack(side="right", fill="y", padx=(0, 10), pady=10)
        self.sidebar_frame.pack_propagate(False)
        
        # إطار المحتوى الرئيسي
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        
        # إعداد القائمة الجانبية
        self.setup_sidebar()
        
        # إعداد الإطارات المختلفة
        self.setup_content_frames()
        
        # عرض الصفحة الأولى
        self.show_frame("personal_info")
    
    def setup_sidebar(self):
        """إعداد القائمة الجانبية"""
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="دراسة الجدوى الشاملة",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(20, 30))
        
        # قائمة الأقسام
        self.sections = [
            ("personal_info", "🧍‍♂️ المعلومات الشخصية"),
            ("project_info", "🧾 وصف المشروع"),
            ("market_analysis", "📊 دراسة السوق والمنافسين"),
            ("swot_analysis", "📈 التحليل الرباعي SWOT"),
            ("marketing_mix", "🧃 المزيج التسويقي"),
            ("production_requirements", "🛠️ مستلزمات الإنتاج"),
            ("financial_study", "📘 الدراسة المالية"),
            ("breakeven_calculator", "📍 نقطة التعادل والحاسبة"),
            ("summary_report", "📋 الملخص والتقرير")
        ]
        
        self.section_buttons = {}
        
        for section_id, section_name in self.sections:
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=section_name,
                font=self.arabic_font,
                height=50,
                corner_radius=10,
                command=lambda s=section_id: self.show_frame(s),
                anchor="e"
            )
            btn.pack(fill="x", padx=20, pady=5)
            self.section_buttons[section_id] = btn
        
        # أزرار الحفظ والتحميل
        save_btn = ctk.CTkButton(
            self.sidebar_frame,
            text="💾 حفظ البيانات",
            font=self.arabic_font,
            height=40,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(fill="x", padx=20, pady=(30, 5))
        
        load_btn = ctk.CTkButton(
            self.sidebar_frame,
            text="📂 تحميل البيانات",
            font=self.arabic_font,
            height=40,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.load_data
        )
        load_btn.pack(fill="x", padx=20, pady=5)
        
        # زر توليد التقرير
        report_btn = ctk.CTkButton(
            self.sidebar_frame,
            text="📄 توليد التقرير النهائي",
            font=self.arabic_font,
            height=40,
            fg_color=("#8b5cf6", "#8b5cf6"),
            command=self.generate_final_report
        )
        report_btn.pack(fill="x", padx=20, pady=5)
    
    def setup_content_frames(self):
        """إعداد إطارات المحتوى المختلفة"""
        self.frames = {}
        
        # إنشاء إطارات مختلفة لكل قسم
        self.frames["personal_info"] = PersonalInfoFrame(self.content_frame, self.data_manager)
        self.frames["project_info"] = ProjectInfoFrame(self.content_frame, self.data_manager)
        self.frames["market_analysis"] = MarketAnalysisFrame(self.content_frame, self.data_manager)
        self.frames["swot_analysis"] = SWOTAnalysisFrame(self.content_frame, self.data_manager)
        self.frames["marketing_mix"] = MarketingMixFrame(self.content_frame, self.data_manager)
        self.frames["production_requirements"] = ProductionRequirementsFrame(self.content_frame, self.data_manager)
        self.frames["financial_study"] = FinancialStudyFrame(self.content_frame, self.data_manager)
        self.frames["breakeven_calculator"] = BreakevenCalculatorFrame(self.content_frame, self.data_manager)
        self.frames["summary_report"] = SummaryReportFrame(self.content_frame, self.data_manager)
        
        # إخفاء جميع الإطارات في البداية
        for frame in self.frames.values():
            frame.pack_forget()
    
    def show_frame(self, frame_name):
        """عرض إطار معين"""
        # إخفاء جميع الإطارات
        for frame in self.frames.values():
            frame.pack_forget()
        
        # عرض الإطار المطلوب
        if frame_name in self.frames:
            self.frames[frame_name].pack(fill="both", expand=True)
            
        # تحديث لون الأزرار
        for section_id, btn in self.section_buttons.items():
            if section_id == frame_name:
                btn.configure(fg_color=("#1f538d", "#1f538d"))
            else:
                btn.configure(fg_color=("#3b82f6", "#3b82f6"))
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            # جمع البيانات من جميع الإطارات
            for frame in self.frames.values():
                if hasattr(frame, 'save_data'):
                    frame.save_data()
            
            # حفظ البيانات في ملف
            self.data_manager.save_to_file()
            messagebox.showinfo("نجح الحفظ", "تم حفظ البيانات بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.data_manager.load_from_file()
            
            # تحديث جميع الإطارات
            for frame in self.frames.values():
                if hasattr(frame, 'load_data'):
                    frame.load_data()
            
            messagebox.showinfo("نجح التحميل", "تم تحميل البيانات بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")
    
    def load_saved_data(self):
        """تحميل البيانات المحفوظة عند بدء التطبيق"""
        try:
            if os.path.exists("data/feasibility_study_data.json"):
                self.data_manager.load_from_file()
                for frame in self.frames.values():
                    if hasattr(frame, 'load_data'):
                        frame.load_data()
        except:
            pass  # تجاهل الأخطاء عند التحميل الأولي
    
    def generate_final_report(self):
        """توليد التقرير النهائي"""
        try:
            # الانتقال إلى صفحة الملخص والتقرير
            self.show_frame("summary_report")
            
            # تحديث بيانات التقرير
            if hasattr(self.frames["summary_report"], 'update_summary'):
                self.frames["summary_report"].update_summary()
                
        except Exception as e:
            messagebox.showerror("خطأ في التقرير", f"حدث خطأ أثناء توليد التقرير:\n{str(e)}")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    os.makedirs("data", exist_ok=True)
    os.makedirs("ui", exist_ok=True)
    os.makedirs("assets", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    
    # تشغيل التطبيق
    app = FeasibilityStudyApp()
    app.run()
