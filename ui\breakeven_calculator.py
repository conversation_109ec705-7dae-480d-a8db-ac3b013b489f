#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نقطة التعادل وحاسبة الاحتساب
Breakeven Point and Calculator Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import math

class BreakevenCalculatorFrame(ctk.CTkFrame):
    def __init__(self, parent, data_manager):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_fonts()
        self.create_widgets()
        self.load_data()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.arabic_font = ("Tajawal", 12)
            self.arabic_font_bold = ("Tajawal", 14, "bold")
            self.arabic_font_large = ("Tajawal", 16, "bold")
        except:
            self.arabic_font = ("Arial Unicode MS", 12)
            self.arabic_font_bold = ("Arial Unicode MS", 14, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 16, "bold")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار قابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(self, label_text="📍 نقطة التعادل والحاسبة")
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="نقطة التعادل + حاسبة الاحتساب",
            font=self.arabic_font_large,
            text_color=("#1f538d", "#ffffff")
        )
        title_label.pack(pady=(0, 20))
        
        # شرح نقطة التعادل
        self.create_explanation_section()
        
        # قسم حساب نقطة التعادل
        self.create_breakeven_section()
        
        # قسم حاسبة الاحتساب الآلية
        self.create_calculator_section()
        
        # قسم النتائج والتحليل
        self.create_results_section()
        
        # أزرار التحكم
        self.create_control_buttons()
        
        # متغيرات الحفظ
        self.fields = {}
    
    def create_explanation_section(self):
        """إنشاء قسم الشرح"""
        explanation_frame = ctk.CTkFrame(self.scrollable_frame)
        explanation_frame.pack(fill="x", padx=20, pady=10)
        
        explanation_title = ctk.CTkLabel(
            explanation_frame,
            text="📚 ما هي نقطة التعادل؟",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        explanation_title.pack(pady=(15, 10))
        
        explanation_text = """
نقطة التعادل (Break-even Point) هي عدد الوحدات التي يجب بيعها لتغطية جميع التكاليف (الثابتة + المتغيرة)، 
بحيث لا يوجد ربح ولا خسارة.

الصيغة:
نقطة التعادل (بالوحدات) = التكاليف الثابتة الشهرية ÷ (سعر بيع الوحدة – تكلفة الوحدة المتغيرة)

أهمية نقطة التعادل:
• تحديد الحد الأدنى للمبيعات المطلوبة
• تقييم جدوى المشروع
• اتخاذ قرارات التسعير
• تخطيط الإنتاج والمبيعات
        """
        
        explanation_label = ctk.CTkLabel(
            explanation_frame,
            text=explanation_text,
            font=self.arabic_font,
            justify="right",
            wraplength=700
        )
        explanation_label.pack(pady=(0, 15))
    
    def create_breakeven_section(self):
        """إنشاء قسم حساب نقطة التعادل"""
        breakeven_frame = ctk.CTkFrame(self.scrollable_frame)
        breakeven_frame.pack(fill="x", padx=20, pady=10)
        
        breakeven_title = ctk.CTkLabel(
            breakeven_frame,
            text="📍 حساب نقطة التعادل (Break-even Point)",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        breakeven_title.pack(pady=(15, 10))
        
        # جدول المدخلات
        inputs_frame = ctk.CTkFrame(breakeven_frame)
        inputs_frame.pack(fill="x", padx=20, pady=10)
        
        inputs_title = ctk.CTkLabel(
            inputs_frame,
            text="المدخلات المطلوبة:",
            font=self.arabic_font_bold
        )
        inputs_title.pack(pady=10)
        
        # التكاليف الثابتة الشهرية
        self.create_input_field(
            inputs_frame,
            "monthly_fixed_costs",
            "التكاليف الثابتة الشهرية",
            "سيتم جلبها تلقائياً من الدراسة المالية"
        )
        
        # متوسط تكلفة الوحدة المتغيرة
        self.create_input_field(
            inputs_frame,
            "average_variable_cost_per_unit",
            "متوسط تكلفة الوحدة المتغيرة",
            "التكلفة المتغيرة لإنتاج وحدة واحدة"
        )
        
        # متوسط سعر بيع الوحدة
        self.create_input_field(
            inputs_frame,
            "average_selling_price_per_unit",
            "متوسط سعر بيع الوحدة",
            "السعر الذي تبيع به الوحدة الواحدة"
        )
        
        # زر الحساب
        calculate_btn = ctk.CTkButton(
            inputs_frame,
            text="🧮 احسب نقطة التعادل",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.calculate_breakeven
        )
        calculate_btn.pack(pady=15)
        
        # النتائج
        results_frame = ctk.CTkFrame(breakeven_frame)
        results_frame.pack(fill="x", padx=20, pady=10)
        
        results_title = ctk.CTkLabel(
            results_frame,
            text="النتائج:",
            font=self.arabic_font_bold
        )
        results_title.pack(pady=10)
        
        # نقطة التعادل بالوحدات
        self.breakeven_units_frame = ctk.CTkFrame(results_frame, fg_color="transparent")
        self.breakeven_units_frame.pack(fill="x", padx=20, pady=5)
        
        breakeven_units_label = ctk.CTkLabel(
            self.breakeven_units_frame,
            text="🔹 نقطة التعادل (عدد الوحدات)",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        breakeven_units_label.pack(side="right", padx=(0, 20))
        
        self.breakeven_units_result = ctk.CTkLabel(
            self.breakeven_units_frame,
            text="0 وحدة",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.breakeven_units_result.pack(side="right", padx=20)
        
        # نقطة التعادل بالإيرادات
        self.breakeven_revenue_frame = ctk.CTkFrame(results_frame, fg_color="transparent")
        self.breakeven_revenue_frame.pack(fill="x", padx=20, pady=5)
        
        breakeven_revenue_label = ctk.CTkLabel(
            self.breakeven_revenue_frame,
            text="🔹 نقطة التعادل (الإيرادات)",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        breakeven_revenue_label.pack(side="right", padx=(0, 20))
        
        self.breakeven_revenue_result = ctk.CTkLabel(
            self.breakeven_revenue_frame,
            text="0 دينار",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.breakeven_revenue_result.pack(side="right", padx=20)
        
        # هامش المساهمة
        self.contribution_margin_frame = ctk.CTkFrame(results_frame, fg_color="transparent")
        self.contribution_margin_frame.pack(fill="x", padx=20, pady=5)
        
        contribution_margin_label = ctk.CTkLabel(
            self.contribution_margin_frame,
            text="🔹 هامش المساهمة للوحدة",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        contribution_margin_label.pack(side="right", padx=(0, 20))
        
        self.contribution_margin_result = ctk.CTkLabel(
            self.contribution_margin_frame,
            text="0 دينار",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.contribution_margin_result.pack(side="right", padx=20)
        
        # نسبة هامش المساهمة
        self.contribution_ratio_frame = ctk.CTkFrame(results_frame, fg_color="transparent")
        self.contribution_ratio_frame.pack(fill="x", padx=20, pady=5)
        
        contribution_ratio_label = ctk.CTkLabel(
            self.contribution_ratio_frame,
            text="🔹 نسبة هامش المساهمة",
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        contribution_ratio_label.pack(side="right", padx=(0, 20))
        
        self.contribution_ratio_result = ctk.CTkLabel(
            self.contribution_ratio_frame,
            text="0%",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.contribution_ratio_result.pack(side="right", padx=20)
    
    def create_input_field(self, parent, field_key, field_label, placeholder):
        """إنشاء حقل إدخال"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", padx=20, pady=5)
        
        # التسمية
        label = ctk.CTkLabel(
            field_frame,
            text=field_label,
            font=self.arabic_font,
            width=300,
            anchor="e"
        )
        label.pack(side="right", padx=(0, 20))
        
        # حقل الإدخال
        entry = ctk.CTkEntry(
            field_frame,
            font=self.arabic_font,
            width=200,
            justify="center",
            placeholder_text=placeholder
        )
        entry.pack(side="right", padx=20)
        entry.bind('<KeyRelease>', lambda e: self.auto_calculate())
        
        # وحدة العملة
        if "cost" in field_key or "price" in field_key:
            currency_label = ctk.CTkLabel(
                field_frame,
                text="دينار",
                font=self.arabic_font,
                width=50
            )
            currency_label.pack(side="right", padx=5)
        
        self.fields[field_key] = entry
    
    def create_calculator_section(self):
        """إنشاء قسم حاسبة الاحتساب الآلية"""
        calculator_frame = ctk.CTkFrame(self.scrollable_frame)
        calculator_frame.pack(fill="x", padx=20, pady=10)
        
        calculator_title = ctk.CTkLabel(
            calculator_frame,
            text="🧮 حاسبة الاحتساب الآلية",
            font=self.arabic_font_bold,
            text_color=("#8b5cf6", "#8b5cf6")
        )
        calculator_title.pack(pady=(15, 10))
        
        # شرح الحاسبة
        calc_desc = ctk.CTkLabel(
            calculator_frame,
            text="تُمكن من إدخال بيانات المنتج تلقائيًا للحصول على النتائج المحسوبة",
            font=self.arabic_font,
            text_color=("#6b7280", "#9ca3af")
        )
        calc_desc.pack(pady=(0, 10))
        
        # جدول المدخلات والنتائج
        calc_table_frame = ctk.CTkFrame(calculator_frame)
        calc_table_frame.pack(fill="x", padx=20, pady=10)
        
        # عناوين الجدول
        calc_headers_frame = ctk.CTkFrame(calc_table_frame, fg_color="transparent")
        calc_headers_frame.pack(fill="x", padx=10, pady=5)
        
        result_header = ctk.CTkLabel(
            calc_headers_frame,
            text="النتيجة المحسوبة",
            font=self.arabic_font_bold,
            width=200,
            fg_color=("#8b5cf6", "#8b5cf6"),
            corner_radius=5
        )
        result_header.pack(side="left", padx=2)
        
        input_header = ctk.CTkLabel(
            calc_headers_frame,
            text="المدخلات",
            font=self.arabic_font_bold,
            width=300,
            fg_color=("#8b5cf6", "#8b5cf6"),
            corner_radius=5
        )
        input_header.pack(side="right", padx=2)
        
        # الصفوف
        calc_items = [
            ("عدد الوحدات المتوقع بيعها", "الإيرادات الشهرية"),
            ("سعر البيع للوحدة", "تكلفة الإنتاج الشهرية"),
            ("تكلفة الوحدة (مواد + أجور + نقل)", "الربح أو الخسارة الشهرية"),
            ("التكاليف الثابتة + المتغيرة", "هامش الربح – الصافي"),
            ("الإهلاك السنوي", "الأرباح الصافية بعد الإهلاك"),
            ("نقطة التعادل", "هل المشروع يغطي تكاليفه أم لا؟")
        ]
        
        self.calc_results = {}
        
        for input_text, result_text in calc_items:
            row_frame = ctk.CTkFrame(calc_table_frame, fg_color="transparent")
            row_frame.pack(fill="x", padx=10, pady=2)
            
            result_label = ctk.CTkLabel(
                row_frame,
                text="0",
                font=self.arabic_font,
                width=200,
                fg_color=("#f3f4f6", "#374151"),
                corner_radius=5
            )
            result_label.pack(side="left", padx=2)
            
            input_label = ctk.CTkLabel(
                row_frame,
                text=input_text,
                font=self.arabic_font,
                width=300,
                anchor="e"
            )
            input_label.pack(side="right", padx=2)
            
            self.calc_results[result_text] = result_label

    def create_results_section(self):
        """إنشاء قسم النتائج والتحليل"""
        results_frame = ctk.CTkFrame(self.scrollable_frame)
        results_frame.pack(fill="x", padx=20, pady=10)

        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 تحليل النتائج والتوصيات",
            font=self.arabic_font_bold,
            text_color=("#059669", "#059669")
        )
        results_title.pack(pady=(15, 10))

        # إطار التحليل
        analysis_frame = ctk.CTkFrame(results_frame)
        analysis_frame.pack(fill="x", padx=20, pady=10)

        self.analysis_text = ctk.CTkTextbox(
            analysis_frame,
            font=self.arabic_font,
            height=150,
            wrap="word"
        )
        self.analysis_text.pack(fill="x", padx=20, pady=15)

        # إطار المؤشرات
        indicators_frame = ctk.CTkFrame(results_frame)
        indicators_frame.pack(fill="x", padx=20, pady=10)

        indicators_title = ctk.CTkLabel(
            indicators_frame,
            text="📈 المؤشرات المالية الرئيسية",
            font=self.arabic_font_bold
        )
        indicators_title.pack(pady=10)

        # مؤشرات في شكل بطاقات
        indicators_grid_frame = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        indicators_grid_frame.pack(fill="x", padx=20, pady=10)

        # بطاقة الربحية
        profitability_card = ctk.CTkFrame(indicators_grid_frame)
        profitability_card.pack(side="right", fill="both", expand=True, padx=5)

        profitability_title = ctk.CTkLabel(
            profitability_card,
            text="💰 الربحية",
            font=self.arabic_font_bold,
            text_color=("#2fa572", "#2fa572")
        )
        profitability_title.pack(pady=10)

        self.profitability_status = ctk.CTkLabel(
            profitability_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.profitability_status.pack(pady=5)

        # بطاقة الجدوى
        feasibility_card = ctk.CTkFrame(indicators_grid_frame)
        feasibility_card.pack(side="right", fill="both", expand=True, padx=5)

        feasibility_title = ctk.CTkLabel(
            feasibility_card,
            text="✅ الجدوى",
            font=self.arabic_font_bold,
            text_color=("#3b82f6", "#3b82f6")
        )
        feasibility_title.pack(pady=10)

        self.feasibility_status = ctk.CTkLabel(
            feasibility_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.feasibility_status.pack(pady=5)

        # بطاقة المخاطر
        risk_card = ctk.CTkFrame(indicators_grid_frame)
        risk_card.pack(side="right", fill="both", expand=True, padx=5)

        risk_title = ctk.CTkLabel(
            risk_card,
            text="⚠️ المخاطر",
            font=self.arabic_font_bold,
            text_color=("#ff6b35", "#ff6b35")
        )
        risk_title.pack(pady=10)

        self.risk_status = ctk.CTkLabel(
            risk_card,
            text="غير محدد",
            font=self.arabic_font,
            fg_color=("#e5e7eb", "#374151"),
            corner_radius=5
        )
        self.risk_status.pack(pady=5)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ نقطة التعادل",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#2fa572", "#2fa572"),
            command=self.save_data
        )
        save_btn.pack(side="right", padx=20)

        # زر المسح
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=self.arabic_font_bold,
            height=40,
            width=150,
            fg_color=("#ff6b35", "#ff6b35"),
            command=self.clear_fields
        )
        clear_btn.pack(side="right", padx=10)

        # زر الانتقال للقسم التالي
        next_btn = ctk.CTkButton(
            buttons_frame,
            text="التالي: الملخص والتقرير ⬅️",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#3b82f6", "#3b82f6"),
            command=self.go_to_next_section
        )
        next_btn.pack(side="left", padx=20)

        # زر العودة للقسم السابق
        prev_btn = ctk.CTkButton(
            buttons_frame,
            text="➡️ السابق: الدراسة المالية",
            font=self.arabic_font_bold,
            height=40,
            width=200,
            fg_color=("#6b7280", "#6b7280"),
            command=self.go_to_prev_section
        )
        prev_btn.pack(side="left", padx=10)

        # زر جلب البيانات من الدراسة المالية
        fetch_btn = ctk.CTkButton(
            buttons_frame,
            text="📥 جلب البيانات من الدراسة المالية",
            font=self.arabic_font_bold,
            height=40,
            width=250,
            fg_color=("#8b5cf6", "#8b5cf6"),
            command=self.fetch_financial_data
        )
        fetch_btn.pack(pady=10)

    def fetch_financial_data(self):
        """جلب البيانات من الدراسة المالية"""
        try:
            financial_data = self.data_manager.get_data("financial_study")

            # حساب التكاليف الثابتة الشهرية
            fixed_costs = financial_data.get('fixed_costs', {})
            monthly_fixed_costs = sum(fixed_costs.values())

            # حساب متوسط تكلفة الوحدة المتغيرة
            products = financial_data.get('products', [])
            if products:
                total_unit_cost = sum(product.get('unit_cost', 0) for product in products)
                avg_unit_cost = total_unit_cost / len(products)

                total_selling_price = sum(product.get('selling_price', 0) for product in products)
                avg_selling_price = total_selling_price / len(products)
            else:
                avg_unit_cost = 0
                avg_selling_price = 0

            # تحديث الحقول
            self.fields['monthly_fixed_costs'].delete(0, tk.END)
            self.fields['monthly_fixed_costs'].insert(0, str(monthly_fixed_costs))

            self.fields['average_variable_cost_per_unit'].delete(0, tk.END)
            self.fields['average_variable_cost_per_unit'].insert(0, str(avg_unit_cost))

            self.fields['average_selling_price_per_unit'].delete(0, tk.END)
            self.fields['average_selling_price_per_unit'].insert(0, str(avg_selling_price))

            # حساب نقطة التعادل تلقائياً
            self.calculate_breakeven()

            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم جلب البيانات من الدراسة المالية بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في جلب البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)

    def auto_calculate(self):
        """حساب تلقائي عند تغيير المدخلات"""
        self.calculate_breakeven()

    def calculate_breakeven(self):
        """حساب نقطة التعادل"""
        try:
            # الحصول على المدخلات
            fixed_costs = float(self.fields['monthly_fixed_costs'].get() or 0)
            variable_cost = float(self.fields['average_variable_cost_per_unit'].get() or 0)
            selling_price = float(self.fields['average_selling_price_per_unit'].get() or 0)

            # التحقق من صحة البيانات
            if selling_price <= variable_cost:
                self.show_error_results("سعر البيع يجب أن يكون أكبر من تكلفة الوحدة المتغيرة")
                return

            if fixed_costs <= 0:
                self.show_error_results("التكاليف الثابتة يجب أن تكون أكبر من صفر")
                return

            # حساب نقطة التعادل
            contribution_margin = selling_price - variable_cost
            breakeven_units = fixed_costs / contribution_margin
            breakeven_revenue = breakeven_units * selling_price
            contribution_margin_ratio = contribution_margin / selling_price

            # تحديث النتائج
            self.breakeven_units_result.configure(
                text=f"{breakeven_units:,.0f} وحدة"
            )

            self.breakeven_revenue_result.configure(
                text=f"{breakeven_revenue:,.0f} دينار"
            )

            self.contribution_margin_result.configure(
                text=f"{contribution_margin:,.0f} دينار"
            )

            self.contribution_ratio_result.configure(
                text=f"{contribution_margin_ratio*100:.1f}%"
            )

            # تحديث حاسبة الاحتساب
            self.update_calculator_results(
                breakeven_units, breakeven_revenue, contribution_margin,
                contribution_margin_ratio, fixed_costs, variable_cost, selling_price
            )

            # تحديث التحليل
            self.update_analysis(
                breakeven_units, breakeven_revenue, contribution_margin_ratio
            )

            # حفظ النتائج في مدير البيانات
            breakeven_data = {
                'monthly_fixed_costs': fixed_costs,
                'average_variable_cost_per_unit': variable_cost,
                'average_selling_price_per_unit': selling_price,
                'breakeven_units': breakeven_units,
                'breakeven_revenue': breakeven_revenue,
                'contribution_margin': contribution_margin,
                'contribution_margin_ratio': contribution_margin_ratio
            }

            self.data_manager.set_data("breakeven_calculator", breakeven_data)

        except ValueError:
            self.show_error_results("يرجى إدخال أرقام صحيحة في جميع الحقول")
        except Exception as e:
            self.show_error_results(f"خطأ في الحساب: {str(e)}")

    def show_error_results(self, error_message):
        """عرض رسالة خطأ في النتائج"""
        self.breakeven_units_result.configure(text="خطأ")
        self.breakeven_revenue_result.configure(text="خطأ")
        self.contribution_margin_result.configure(text="خطأ")
        self.contribution_ratio_result.configure(text="خطأ")

        # عرض رسالة الخطأ
        error_label = ctk.CTkLabel(
            self.scrollable_frame,
            text=f"❌ {error_message}",
            font=self.arabic_font_bold,
            text_color=("#dc2626", "#ef4444")
        )
        error_label.pack(pady=10)
        self.after(5000, error_label.destroy)

    def update_calculator_results(self, breakeven_units, breakeven_revenue, contribution_margin,
                                 contribution_margin_ratio, fixed_costs, variable_cost, selling_price):
        """تحديث نتائج حاسبة الاحتساب"""
        try:
            # الحصول على بيانات إضافية من الدراسة المالية
            financial_data = self.data_manager.get_data("financial_study")

            # حساب الإيرادات الشهرية المتوقعة
            products = financial_data.get('products', [])
            monthly_revenue = sum(product.get('revenue', 0) for product in products)

            # حساب تكلفة الإنتاج الشهرية
            variable_costs = financial_data.get('variable_costs', {})
            monthly_variable_costs = sum(variable_costs.values())
            monthly_production_cost = fixed_costs + monthly_variable_costs

            # حساب الربح الشهري
            monthly_profit = monthly_revenue - monthly_production_cost

            # حساب هامش الربح الصافي
            profit_margin = (monthly_profit / monthly_revenue * 100) if monthly_revenue > 0 else 0

            # حساب الأرباح السنوية بعد الإهلاك
            annual_depreciation = financial_data.get('annual_depreciation', 0)
            annual_profit_after_depreciation = (monthly_profit * 12) - annual_depreciation

            # تحديد حالة تغطية التكاليف
            covers_costs = "نعم، يغطي التكاليف" if monthly_profit > 0 else "لا، لا يغطي التكاليف"

            # تحديث النتائج
            self.calc_results["الإيرادات الشهرية"].configure(
                text=f"{monthly_revenue:,.0f} دينار"
            )

            self.calc_results["تكلفة الإنتاج الشهرية"].configure(
                text=f"{monthly_production_cost:,.0f} دينار"
            )

            self.calc_results["الربح أو الخسارة الشهرية"].configure(
                text=f"{monthly_profit:,.0f} دينار"
            )

            self.calc_results["هامش الربح – الصافي"].configure(
                text=f"{profit_margin:.1f}%"
            )

            self.calc_results["الأرباح الصافية بعد الإهلاك"].configure(
                text=f"{annual_profit_after_depreciation:,.0f} دينار"
            )

            self.calc_results["هل المشروع يغطي تكاليفه أم لا؟"].configure(
                text=covers_costs
            )

        except Exception as e:
            print(f"خطأ في تحديث حاسبة الاحتساب: {e}")

    def update_analysis(self, breakeven_units, breakeven_revenue, contribution_margin_ratio):
        """تحديث تحليل النتائج"""
        try:
            # الحصول على بيانات إضافية
            financial_data = self.data_manager.get_data("financial_study")
            products = financial_data.get('products', [])

            # حساب المبيعات المتوقعة
            expected_monthly_sales = sum(product.get('quantity', 0) for product in products)
            expected_monthly_revenue = sum(product.get('revenue', 0) for product in products)

            # تحليل الجدوى
            analysis_text = f"""
📊 تحليل نقطة التعادل والجدوى المالية:

🔹 نقطة التعادل: {breakeven_units:,.0f} وحدة شهرياً
🔹 الإيرادات المطلوبة للتعادل: {breakeven_revenue:,.0f} دينار شهرياً
🔹 هامش المساهمة: {contribution_margin_ratio*100:.1f}%

📈 مقارنة مع التوقعات:
• المبيعات المتوقعة: {expected_monthly_sales:,.0f} وحدة شهرياً
• الإيرادات المتوقعة: {expected_monthly_revenue:,.0f} دينار شهرياً

"""

            # تحليل الوضع
            if expected_monthly_sales >= breakeven_units:
                safety_margin = ((expected_monthly_sales - breakeven_units) / breakeven_units) * 100
                analysis_text += f"""
✅ التقييم: المشروع مجدي مالياً
• المبيعات المتوقعة تتجاوز نقطة التعادل بنسبة {safety_margin:.1f}%
• هامش الأمان: {expected_monthly_sales - breakeven_units:,.0f} وحدة

💡 التوصيات:
• المشروع قابل للتنفيذ
• يُنصح بالبدء في التنفيذ
• مراقبة المبيعات للتأكد من تحقيق الأهداف
"""

                # تحديث المؤشرات
                self.profitability_status.configure(
                    text="مربح",
                    fg_color=("#dcfce7", "#166534")
                )
                self.feasibility_status.configure(
                    text="مجدي",
                    fg_color=("#dbeafe", "#1e40af")
                )
                self.risk_status.configure(
                    text="منخفض",
                    fg_color=("#dcfce7", "#166534")
                )

            else:
                shortage = breakeven_units - expected_monthly_sales
                shortage_percentage = (shortage / breakeven_units) * 100
                analysis_text += f"""
⚠️ التقييم: المشروع يحتاج إعادة نظر
• المبيعات المتوقعة أقل من نقطة التعادل بـ {shortage:,.0f} وحدة ({shortage_percentage:.1f}%)
• نقص في الإيرادات: {(breakeven_revenue - expected_monthly_revenue):,.0f} دينار

💡 التوصيات:
• زيادة المبيعات المتوقعة
• تقليل التكاليف الثابتة
• زيادة سعر البيع
• تحسين هامش الربح
"""

                # تحديث المؤشرات
                self.profitability_status.configure(
                    text="غير مربح",
                    fg_color=("#fef2f2", "#991b1b")
                )
                self.feasibility_status.configure(
                    text="غير مجدي",
                    fg_color=("#fef2f2", "#991b1b")
                )
                self.risk_status.configure(
                    text="عالي",
                    fg_color=("#fef2f2", "#991b1b")
                )

            # إضافة نصائح عامة
            analysis_text += f"""

📋 نصائح لتحسين الأداء:
• مراجعة استراتيجية التسعير
• تحسين كفاءة العمليات لتقليل التكاليف
• زيادة الجهود التسويقية
• تنويع المنتجات لزيادة المبيعات
• مراقبة المنافسين وأسعارهم

🎯 أهداف قصيرة المدى:
• تحقيق {breakeven_units:,.0f} وحدة مبيعات شهرياً كحد أدنى
• الوصول لإيرادات {breakeven_revenue:,.0f} دينار شهرياً
• المحافظة على هامش مساهمة {contribution_margin_ratio*100:.1f}%
"""

            # تحديث النص
            self.analysis_text.delete("1.0", tk.END)
            self.analysis_text.insert("1.0", analysis_text)

        except Exception as e:
            print(f"خطأ في تحديث التحليل: {e}")

    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {}

            # حفظ المدخلات
            for field_key, entry in self.fields.items():
                try:
                    data[field_key] = float(entry.get() or 0)
                except ValueError:
                    data[field_key] = 0

            # حساب النتائج وحفظها
            if data['average_selling_price_per_unit'] > data['average_variable_cost_per_unit']:
                contribution_margin = data['average_selling_price_per_unit'] - data['average_variable_cost_per_unit']
                data['breakeven_units'] = data['monthly_fixed_costs'] / contribution_margin
                data['breakeven_revenue'] = data['breakeven_units'] * data['average_selling_price_per_unit']
                data['contribution_margin'] = contribution_margin
                data['contribution_margin_ratio'] = contribution_margin / data['average_selling_price_per_unit']

            self.data_manager.set_data("breakeven_calculator", data)

            # رسالة نجاح
            success_label = ctk.CTkLabel(
                self.scrollable_frame,
                text="✅ تم حفظ نقطة التعادل بنجاح!",
                font=self.arabic_font_bold,
                text_color=("#059669", "#10b981")
            )
            success_label.pack(pady=10)
            self.after(3000, success_label.destroy)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.scrollable_frame,
                text=f"❌ خطأ في حفظ البيانات: {str(e)}",
                font=self.arabic_font_bold,
                text_color=("#dc2626", "#ef4444")
            )
            error_label.pack(pady=10)
            self.after(5000, error_label.destroy)

    def load_data(self):
        """تحميل البيانات"""
        try:
            data = self.data_manager.get_data("breakeven_calculator")

            # تحميل المدخلات
            for field_key, entry in self.fields.items():
                value = data.get(field_key, 0)
                entry.delete(0, tk.END)
                entry.insert(0, str(value))

            # إعادة حساب النتائج
            if any(data.values()):
                self.calculate_breakeven()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        # مسح المدخلات
        for entry in self.fields.values():
            entry.delete(0, tk.END)

        # مسح النتائج
        self.breakeven_units_result.configure(text="0 وحدة")
        self.breakeven_revenue_result.configure(text="0 دينار")
        self.contribution_margin_result.configure(text="0 دينار")
        self.contribution_ratio_result.configure(text="0%")

        # مسح حاسبة الاحتساب
        for result_label in self.calc_results.values():
            result_label.configure(text="0")

        # مسح التحليل
        self.analysis_text.delete("1.0", tk.END)

        # إعادة تعيين المؤشرات
        self.profitability_status.configure(
            text="غير محدد",
            fg_color=("#e5e7eb", "#374151")
        )
        self.feasibility_status.configure(
            text="غير محدد",
            fg_color=("#e5e7eb", "#374151")
        )
        self.risk_status.configure(
            text="غير محدد",
            fg_color=("#e5e7eb", "#374151")
        )

    def go_to_next_section(self):
        """الانتقال للقسم التالي"""
        self.save_data()
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("summary_report")

    def go_to_prev_section(self):
        """العودة للقسم السابق"""
        if hasattr(self.master.master, 'show_frame'):
            self.master.master.show_frame("financial_study")
